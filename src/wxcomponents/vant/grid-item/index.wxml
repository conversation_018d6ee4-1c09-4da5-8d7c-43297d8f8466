<wxs src="../wxs/utils.wxs" module="utils" />
<wxs src="./index.wxs" module="computed" />

<view
  class="custom-class {{ utils.bem('grid-item', { square }) }}"
  style="{{ computed.wrapperStyle({ square, gutter, columnNum, index }) }}"
  bindtap="onClick"
>
  <view
    class="content-class {{ utils.bem('grid-item__content', [direction, { center, square, reverse, clickable, surround: border && gutter }]) }} {{ border ? 'van-hairline--surround' : '' }}"
    style="{{ computed.contentStyle({ square, gutter }) }}"
  >
    <block wx:if="{{ useSlot }}">
      <slot />
    </block>
    <block wx:else>
      <view class="van-grid-item__icon icon-class">
        <van-icon wx:if="{{ icon }}" name="{{ icon }}" color="{{ iconColor }}" class-prefix="{{ iconPrefix }}" dot="{{ dot }}" info="{{ badge || info }}" size="{{ iconSize }}" />
        <slot wx:else name="icon"></slot>
      </view>
      <view class="van-grid-item__text text-class">
        <text wx:if="{{ text }}">{{ text }}</text>
        <slot wx:else name="text"></slot>
      </view>
    </block>
  </view>
</view>
