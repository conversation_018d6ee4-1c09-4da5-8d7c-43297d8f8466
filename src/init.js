/**
 * <AUTHOR>
 * @description 监听路由页面、监听请求
 */

// 导入 useUserStore 和 useFromPage 函数，用于获取用户信息和当前页信息
import monkey from '@/monkey';

// 监听路由页面、监听请求
export default async function () {
	let Debug = false;
	//自定义路由拦截, 需要登录的页面
	const { "router": { needLogin, visitor, login, verifyUserInfo } } = monkey.$config;
	// 监听事件
	let list = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];
	// 用遍历的方式分别为,uni.navigateTo,uni.redirectTo,uni.reLaunch,uni.switchTab这4个路由方法添加拦截器
	list.forEach(item => {
		uni.addInterceptor(item, {
			// 调用前拦截
			invoke(e) {
				uni.hideLoading();
				// 是否登录
				const hasLogin = uni.getStorageSync('hasLogin');
				//获取用户的token
				const token = uni.getStorageSync('token');
				// token是否已失效
				// tokenExpired = uni.getStorageSync('uni_id_token_expired') < Date.now(),
				let tokenExpired = false;
				// 获取要跳转的页面路径（url去掉"?"和"?"后的参数）
				let url = e.url.split('?')[0];
				// 获取要前往的页面路径（即url去掉"?"和"?"后的参数）
				const pages = getCurrentPages();
				if (!pages.length) {
					console.log("首页启动调用了");
					console.log(e);
					return e;
				}
				// const fromUrl = pages[pages.length - 1].route;
				// let inLoginPage = fromUrl.split('/')[2] == 'login';
				// 控制登录优先级,判断当前窗口是否为登录页面，如果是则不重定向路由
				// if (url == '/pages/ucenter/login/inde' && !inLoginPage) {
				// 	//一键登录（univerify）、账号（username）、验证码登录（短信smsCode）
				// 	if (login[0] == 'username')
				// 		e.url = "/pages/ucenter/login-page/pwd-login/pwd-login"
				// 	else {
				// 		//添加参数之前判断是否带了`？`号如果没有就补上，因为当开发场景本身有参数的情况下是已经带了`？`号
				// 		if (e.url == url)
				// 			e.url += '?'
				// 		e.url += "type=" + login[0]
				// 	}
				// } else {
				// 拦截强制登录页面
				let pass = true;
				// 校验需要登录页面
				if (needLogin) {
					pass = needLogin.every((item) => {
						if (typeof (item) == 'object' && item.pattern)
							return !item.pattern.test(url)
						return url != item
					});
				}
				console.log("🚀 ~ invoke ~ pass:", pass, url)
				// 校验不需要登录页面白名单
				if (visitor) {
					pass = visitor.some((item) => {
						if (typeof (item) == 'object' && item.pattern) {
							return item.pattern.test(url)
						}
						return url == item;
					})
				}
				if (!pass && (token == '' || !hasLogin)) {
					monkey.$stores.useAuthModalStore().open();
					return false;
				}
				// 校验需要验证用户信息页面
				// if (verifyUserInfo) {
				// 	pass = verifyUserInfo.some((item) => {
				// 		return url == item;
				// 	});
				// 	// const userInfo = useUserStore().GetUInfo();
				// 	// console.log(userInfo);
				// 	// if (userInfo && !userInfo.sjh && pass) {
				// 	// 	reLaunch('/modules/ucenter/info/index', 800);
				// 	// 	return false;
				// 	// }
				// }
				// }
				return e;
			},
			fail(err) { // 失败回调拦截 
				if (Debug) {
					uni.showModal({
						content: JSON.stringify(err),
						showCancel: false
					});
				}
			}
		});
	});
	// 监听请求方法
	let callFunctionOption;
	// 添加防抖控制变量
	let isShowingError = false;

	uni.addInterceptor('request', {
		invoke(request) {
			console.log("request", request);
			if (request.loading.show)
				uni.showLoading({
					title: request?.loading?.title, mask: true, fail: () => {
						uni.hideLoading();
					}
				});
			// 添加默认URl基本链接
			// #ifdef MP-WEIXIN
			if (request.url) {
				if (request.url.indexOf('http') < 0)
					request.url = monkey.$config.target.BASEURL + request.url;
			}
			// #endif
			// 给所有请求添加自定义header
			request.header = request.header || {};

			if (request.token) {
				// 根据请求源设置对应的 Authorization token
				if (request.source === 'all') {
					// 当 source 为 'all' 时，同时传递两个 token
					const authineToken = uni.getStorageSync('authineToken');
					const normalToken = uni.getStorageSync('token');

					if (authineToken) {
						request.header['Authorization'] = 'Bearer ' + authineToken;
					}
					if (normalToken) {
						request.header['Author'] = normalToken;
					}
				} else {
					// 原有逻辑：根据 source 类型选择对应的 token
					const isAuthineRequest = request.source === 'authine';
					const tokenKey = isAuthineRequest ? 'authineToken' : 'token';
					const token = uni.getStorageSync(tokenKey);

					// 设置 Authorization header（如果 token 存在）
					if (token) {
						request.header[isAuthineRequest ? 'Authorization' : 'Author'] = isAuthineRequest ? 'Bearer ' + token : token;
					}
				}
			}

			// 调试信息（开发环境）
			if (Debug) {
				if (request.source === 'all') {
					const authineToken = uni.getStorageSync('authineToken');
					const normalToken = uni.getStorageSync('token');
					console.log(`🔐 Request auth: source=${request.source}, hasAuthineToken=${!!authineToken}, hasNormalToken=${!!normalToken}`);
				} else {
					const isAuthineRequest = request.source === 'authine';
					const tokenKey = isAuthineRequest ? 'authineToken' : 'token';
					const token = uni.getStorageSync(tokenKey);
					console.log(`🔐 Request auth: source=${request.source}, tokenKey=${tokenKey}, hasToken=${!!token}`);
				}
			}

			// 防止缓存
			if (request.method === 'POST' && request.header['Content-Type'] !== 'multipart/form-data') {
				request.body = {
					...request.body,
					_t: Date.parse(new Date()) / 1000
				}
			} else if (request.method === 'GET') {
				request.params = {
					_t: Date.parse(new Date()) / 1000,
					...request.params
				}
			}
			// 复制回调方法
			callFunctionOption = request;
			return request
		},
		fail(err) {
			if (Debug) {
				monkey.$helper.toast.modal({
					title: '提示',
					content: JSON.stringify(e),
					showCancel: false
				});
			} else {
				monkey.$helper.toast.modal({
					title: '提示',
					content: "系统维护中，请稍后再试！",
					confirmText: '知道了',
					showCancel: false
				});
			}
			monkey.$helper.toast.hideLoading();
			//如果执行错误，检查是否断网
			uni.getNetworkType({
				complete: res => {
					console.log(res);
					if (res.networkType == 'none') {
						uni.showToast({
							title: '手机网络异常',
							icon: 'none'
						});
						console.log('手机网络异常');
						let callBack = res => {
							console.log(res);
							if (res.isConnected) {
								uni.showToast({
									title: '恢复联网自动重新执行',
									icon: 'none'
								});
								console.log(res.networkType, "恢复联网自动重新执行");
								uni.offNetworkStatusChange(e => {
									console.log("移除监听成功", e);
								})
								//恢复联网自动重新执行
								uni.request(callFunctionOption)
							}
						}
					}
				}
			});
		},
		success: (e) => {
			console.log(e);
			// 如果已经在显示错误，则直接返回
			switch (e.data.errcode || e.data.code) {
				case 401:
					if (e.data.errmsg && e.data.errmsg == 'Full authentication is required to access this resource') {
						monkey.$helper.toast.error('登录已过期，请重新登录');
						monkey.$stores.useAuthModalStore().open();
					}
					break;
				case 404:
				case 7005: // 用户已认证
				case 100005: // 频繁重复提交
				case 999999:
				case 50000:
					monkey.$helper.toast.error(e.data.errmsg || e.data.msg);
					break;
				case 550009:
					monkey.$helper.toast.error('服务存在异常，请稍后再试');
					break;
				case 8000:
					monkey.$helper.toast.error(e.data.errmsg || e.data.msg);
					monkey.$stores.useAuthModalStore().open();
					break;
				default:
					monkey.$helper.toast.hideLoading();
					console.log('code的值是：' + e.data.code, '可以在上面添加case，自动处理响应体');
					break;
			}
		}
	});
}
