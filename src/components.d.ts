/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    UiAuth: typeof import('./monkey/components/ui-auth/ui-auth.vue')['default']
    UiHomeActions: typeof import('./monkey/components/ui-home/ui-home-actions.vue')['default']
    'UiHomeActions copy': typeof import('./monkey/components/ui-home/ui-home-actions copy.vue')['default']
    'UiHomeActions copy 2': typeof import('./monkey/components/ui-home/ui-home-actions copy 2.vue')['default']
    UiHomeActionsTwo: typeof import('./monkey/components/ui-home/ui-home-actions-two.vue')['default']
    UiHomeBuy: typeof import('./monkey/components/ui-home/ui-home-buy.vue')['default']
    'UiHomeBuy copy': typeof import('./monkey/components/ui-home/ui-home-buy copy.vue')['default']
    UiHomeMedicine: typeof import('./monkey/components/ui-home/ui-home-medicine.vue')['default']
    UiHomePoints: typeof import('./monkey/components/ui-home/ui-home-points.vue')['default']
    UiHomePrice: typeof import('./monkey/components/ui-home/ui-home-price.vue')['default']
    UiHomeRisk: typeof import('./monkey/components/ui-home/ui-home-risk.vue')['default']
    UiHomeTitle: typeof import('./monkey/components/ui-home/ui-home-title.vue')['default']
    UiHomeTitle2: typeof import('./monkey/components/ui-home/ui-home-title2.vue')['default']
    UiHomeTrading: typeof import('./monkey/components/ui-home/ui-home-trading.vue')['default']
    UiPopup: typeof import('./monkey/components/ui-popup/ui-popup.vue')['default']
    UiUserBo: typeof import('./monkey/components/ui-user/ui-user-bo.vue')['default']
    UiUserCard: typeof import('./monkey/components/ui-user/ui-user-card.vue')['default']
    UiUserQuick: typeof import('./monkey/components/ui-user/ui-user-quick.vue')['default']
    UiUserVerify: typeof import('./monkey/components/ui-user/ui-user-verify.vue')['default']
  }
}
