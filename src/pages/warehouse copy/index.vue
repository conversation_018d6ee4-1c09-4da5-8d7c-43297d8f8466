<template>
  <ly-layout isArcBg :active="1">
    <!-- 头部区域 -->
    <div class="relative z-10 px-4 pt-4 pb-6">
      <div class="flex items-center justify-between mb-4">
        <div class="text-xl font-bold text-white">我的仓库</div>
      </div>

      <!-- 统计卡片 -->
      <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
        <div class="flex justify-between items-center mb-3">
          <div class="text-sm font-medium text-gray-600">仓库总览</div>
          <div class="text-xs text-blue-500">查看明细</div>
        </div>
        <div class="grid grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-lg font-bold text-blue-600">3</div>
            <div class="text-xs text-gray-500">仓库数量</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-green-600">85%</div>
            <div class="text-xs text-gray-500">平均使用率</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-amber-600">12</div>
            <div class="text-xs text-gray-500">预警产品</div>
          </div>
        </div>
      </div>

      <!-- 仓库列表 -->
      <div>
        <div class="flex items-center justify-between mb-4">
          <div class="text-base font-bold text-gray-800">我的仓库列表</div>
        </div>

        <!-- 仓库卡片列表 -->
        <div class="space-y-4">
          <div v-for="(warehouse, index) in warehouseList" :key="index" class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100 active:bg-gray-50 transition-colors">
            <div class="relative">
              <div class="h-28 w-full bg-gradient-to-r" :class="warehouse.bgColor"></div>
              <div class="absolute top-0 left-0 w-full h-full p-4 flex flex-col justify-between">
                <div class="flex justify-between items-start">
                  <div class="bg-white/90 backdrop-blur-sm py-1 px-3 rounded-full text-sm font-medium" :class="warehouse.statusColor">
                    {{ warehouse.status }}
                  </div>
                </div>
                <div class="flex items-end justify-between">
                  <div>
                    <div class="text-lg font-bold text-white drop-shadow-sm">{{ warehouse.name }}</div>
                    <div class="text-xs text-white/90">{{ warehouse.location }}</div>
                  </div>
                  <div class="text-white text-xs bg-white/20 backdrop-blur-sm py-1 px-2 rounded-full">
                    {{ warehouse.type }}
                  </div>
                </div>
              </div>
            </div>

            <div class="p-4">
              <div class="flex items-center justify-between mb-3">
                <div class="text-sm text-gray-500">使用率</div>
                <div class="text-sm font-medium">{{ warehouse.usageRate }}%</div>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div class="h-2 rounded-full" :class="warehouse.progressColor" :style="{ width: `${warehouse.usageRate}%` }"></div>
              </div>

              <div class="grid grid-cols-3 gap-3 mb-3">
                <div class="bg-gray-50 rounded-lg p-2 text-center">
                  <div class="text-sm font-medium text-gray-800">{{ warehouse.totalItems }}</div>
                  <div class="text-xs text-gray-500">总库存</div>
                </div>
                <div class="bg-gray-50 rounded-lg p-2 text-center">
                  <div class="text-sm font-medium text-gray-800">{{ warehouse.inboundItems }}</div>
                  <div class="text-xs text-gray-500">待入库</div>
                </div>
                <div class="bg-gray-50 rounded-lg p-2 text-center">
                  <div class="text-sm font-medium text-gray-800">{{ warehouse.outboundItems }}</div>
                  <div class="text-xs text-gray-500">待出库</div>
                </div>
              </div>

              <div class="flex justify-between">
                <div class="text-xs text-gray-500">{{ warehouse.lastUpdateTime }}</div>
                <div class="text-xs text-blue-500" @click="handleDetail(warehouse)">查看详情</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 添加仓库按钮 -->
        <div class="mt-6 mb-8">
          <div class="border border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center bg-gray-50 active:bg-gray-100 transition-colors">
            <div class="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mb-3">
              <div class="i-mdi-plus text-blue-600 text-xl"></div>
            </div>
            <div class="text-sm font-medium text-gray-700 mb-1">添加新仓库</div>
            <div class="text-xs text-gray-500">点击添加您的仓库</div>
          </div>
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  interface Warehouse {
    name: string;
    location: string;
    type: string;
    status: string;
    statusColor: string;
    bgColor: string;
    usageRate: number;
    progressColor: string;
    totalItems: number;
    inboundItems: number;
    outboundItems: number;
    lastUpdateTime: string;
  }

  // 仓库数据
  const warehouseList = reactive<Warehouse[]>([
    {
      name: '杭州仓库',
      location: '浙江省杭州市余杭区',
      type: '自有仓库',
      status: '正常运营',
      statusColor: 'text-green-600',
      bgColor: 'from-blue-500 to-sky-600',
      usageRate: 75,
      progressColor: 'bg-green-500',
      totalItems: 1250,
      inboundItems: 32,
      outboundItems: 48,
      lastUpdateTime: '今天 14:30 更新',
    },
    {
      name: '广州分仓',
      location: '广东省广州市白云区',
      type: '第三方仓储',
      status: '库存预警',
      statusColor: 'text-amber-600',
      bgColor: 'from-amber-500 to-orange-600',
      usageRate: 92,
      progressColor: 'bg-amber-500',
      totalItems: 875,
      inboundItems: 21,
      outboundItems: 53,
      lastUpdateTime: '今天 10:15 更新',
    },
    {
      name: '上海仓库',
      location: '上海市浦东新区',
      type: '合作仓库',
      status: '正常运营',
      statusColor: 'text-green-600',
      bgColor: 'from-indigo-500 to-purple-600',
      usageRate: 45,
      progressColor: 'bg-blue-500',
      totalItems: 2130,
      inboundItems: 67,
      outboundItems: 42,
      lastUpdateTime: '今天 08:45 更新',
    },
  ]);

  const handleDetail = (warehouse: Warehouse) => {
    monkey.$router.navigateTo(`/modules/home/<USER>/detail?id=${warehouse.id}`);
  };
</script>

<style lang="scss" scoped></style>
