<template>
  <ly-layout is-arc-bg>
    <div class="header sticky top-0 z-20 text-white px-4 pb-4" :style="{ backgroundColor: navBarBgColor }">
      <ly-status-bar />
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="h-10 w-10 bg-white/20 rounded-lg flex items-center justify-center mr-3">
            <image class="size-full rounded-lg" :src="monkey.$config.about.logo"></image>
          </div>
          <div>
            <div class="text-lg font-bold">{{ monkey.$config.about.appName }}</div>
            <div class="text-xs text-green-100">{{ monkey.$config.about.slogan }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-1 px-4 b-20 relative z-10">
      <div class="swiper rounded-lg h-40 mb-4 shadow-lg">
        <image class="size-full rounded-lg" src="https://img1.baidu.com/it/u=3499617352,759393670&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500" />
      </div>
      <div class="mb-4">
        <ui-home-buy />
      </div>
      <div class="mb-4">
        <div class="bg-white/80 rounded-lg px-4 py-5 shadow-md">
        <div class="mb-4">
          <ly-title title="快捷操作" />
        </div>
          <ui-home-actions />
        </div>
      </div>
      <div class="mb-4">
        <!-- <div class="mb-4">
          <ly-title title="交易大厅" more="更多" @more="handleTradingClick" />
        </div> -->
        <ui-home-trading>
          <template #title>
            <div class="mb-4">
              <ly-title title="交易大厅" more="更多" @more="handleTradingClick" />
            </div>
          </template>
        </ui-home-trading>
      </div>
      <div class="mb-4">
        <!-- <div class="mb-4">
          <ly-title title="价格动态" more="更多" @more="handlePriceClick" />
        </div> -->
        <ui-home-price>
          <template #title>
            <div class="mb-4">
              <ly-title title="药材行情" more="更多" @more="handlePriceClick" />
            </div>
          </template>
        </ui-home-price>
      </div>
      <div class="mb-4">
        <div class="mb-4">
          <ly-title title="产品推荐" more="更多" @more="handleGoodsListClick" />
        </div>
        <ly-goods-list @click="handleGoodsClick" />
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  // 导入配置
  import monkey from '@/monkey';
  import type { Goods } from '@/monkey/types';
  // 导航栏背景颜色
  const navBarBgColor: Ref<string> = ref('rgba(0, 109, 187, 0)');
  /**
   * 监听页面滚动
   * @param e 滚动事件
   */
  const handlePageScroll = (e: Page.PageScrollOption) => {
    const scrollTop = e.scrollTop;
    // 设置滚动阈值，这里设为200，您可以根据需要调整
    const threshold = 200;

    if (scrollTop > threshold) {
      // 超过阈值时设置不透明背景
      navBarBgColor.value = 'rgba(0, 109, 187, 1)';
    } else {
      // 计算透明度
      const opacity = scrollTop / threshold;
      navBarBgColor.value = `rgba(0, 109, 187, ${opacity})`;
    }
  };

  /**
   * 产品点击
   * @param product 产品
   */
  const handleGoodsClick = (product: Goods) => {
    monkey.$router.navigateTo('/modules/goods/detail/index?id=' + product.id);
  };

  /**
   * 交易大厅点击
   */
  const handleTradingClick = () => {
    monkey.$router.navigateTo('/modules/home/<USER>/index');
  };

  /**
   * 产品列表点击
   */
  const handleGoodsListClick = () => {
    monkey.$router.navigateTo('/modules/goods/list/index');
  };

  /**
   * 药材行情点击
   */
  const handleMedicineClick = () => {
    monkey.$router.navigateTo('/modules/home/<USER>/index');
  };

  /**
   * 药材行情点击
   */
  const handlePriceClick = () => {
    monkey.$router.switchTab('/pages/medicine/index');
  };

  /**
   * 监听页面滚动
   * @param e 滚动事件
   */
  onPageScroll((e: Page.PageScrollOption) => {
    handlePageScroll(e);
  });
</script>

<style lang="scss" scoped>
  .swiper {
    background: linear-gradient(to right, rgb(0, 109, 187), rgb(0, 128, 212), rgb(77, 166, 224));
  }
</style>
