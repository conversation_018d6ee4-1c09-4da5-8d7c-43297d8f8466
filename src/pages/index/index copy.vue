<template>
  <ly-layout is-arc-bg :active="0">
    <div class="header sticky top-0 z-20 text-white px-4 pb-2" :style="{ backgroundColor: navBarBgColor }">
      <ly-status-bar />
      <div class="flex items-center justify-between h-[44px]">
        <div class="flex items-center gap-2">
          <!-- <div class="h-10 w-10 bg-white/20 rounded-lg flex items-center justify-center mr-3">
            <image class="size-full rounded-lg"></image>
          </div> -->
          <div class="flex flex-col h-full justify-center gap-1">
            <div class="text-sm font-bold leading-3">{{ monkey.$config.about.appName }}</div>
            <div class="text-xs text-green-100 leading-3">{{ monkey.$config.about.slogan }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-1 px-4 b-20 relative z-10">
      <div class="swiper rounded-lg h-40 mb-4 shadow-lg">
        <swiper class="size-full rounded-lg" :autoplay="true" :interval="3000" :circular="true" :indicator-dots="true" :indicator-color="'#fff'" :indicator-active-color="'#000'">
          <swiper-item class="size-full rounded-lg" v-for="(item, index) in swiperList" :key="index">
            <image class="size-full rounded-lg" :src="item.url" />
          </swiper-item>
        </swiper>
      </div>
      <div class="mb-4">
        <ui-home-points />
      </div>
      <div class="mb-4">
        <ui-home-actions />
      </div>
      <div class="mb-4">
        <!-- <div class="mb-4">
          <ly-title title="交易大厅" more="更多" @more="handleTradingClick" />
        </div> -->
        <ui-home-trading>
          <template #title>
            <div class="mb-4">
              <ly-title title="交易大厅" more="更多" @more="handleTradingClick" />
            </div>
          </template>
        </ui-home-trading>
      </div>
      <div class="mb-4">
        <!-- <div class="mb-4">
          <ly-title title="价格动态" more="更多" @more="handlePriceClick" />
        </div> -->
        <ui-home-price>
          <template #title>
            <div class="mb-4">
              <ly-title title="药材行情" more="更多" @more="handlePriceClick" />
            </div>
          </template>
        </ui-home-price>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  uni.hideTabBar();
  // 导入配置
  import monkey from '@/monkey';
  import type { Goods } from '@/monkey/types';
  // 导航栏背景颜色
  const navBarBgColor: Ref<string> = ref('rgba(0, 109, 187, 0)');

  const { open, visible } = monkey.$hooks.useAuthModal();

  const swiperList = ref<string[]>([
    {
      url: 'https://bjnkdpxshop.oss-cn-beijing.aliyuncs.com/2025/07/08b087efcb738a42fc95371ab37531f9aa35DF6A64-88BD-49a0-95EC-9B296F6661C3.png',
    },
    {
      url: 'https://bjnkdpxshop.oss-cn-beijing.aliyuncs.com/2025/07/0854c36d7c954a49fa979a821c44016fa80148ABA6-066F-4f50-B41C-5E32F7656BF3.png',
    },
    {
      url: 'https://bjnkdpxshop.oss-cn-beijing.aliyuncs.com/2025/07/085a90bdf4f0284a6b8087c54248418997A9A47E13-46F6-4ae0-9BFC-54F636FC0CD6.png',
    },
    {
      url: 'https://bjnkdpxshop.oss-cn-beijing.aliyuncs.com/2025/07/085094ee2694ca490bac7b3c76a8366a85FF4D278D-3866-4373-8A7A-5681415E8665.png',
    },
  ]);

  const handleAuthModal = () => {
    console.log('🚀 ~  monkey.$hooks:', monkey.$hooks.useAuthModal());
    console.log('handleAuthModal');
    open();
    console.log('🚀 ~ visible:', monkey.$hooks.useAuthModal().visible);
  };
  /**
   * 监听页面滚动
   * @param e 滚动事件
   */
  const handlePageScroll = (e: Page.PageScrollOption) => {
    const scrollTop = e.scrollTop;
    // 设置滚动阈值，这里设为200，您可以根据需要调整
    const threshold = 200;

    if (scrollTop > threshold) {
      // 超过阈值时设置不透明背景
      navBarBgColor.value = 'rgb(16, 185, 129, 1)'; // 设置不透明背景
    } else {
      // 计算透明度
      const opacity = scrollTop / threshold;
      navBarBgColor.value = `rgba(16, 185, 129, ${opacity})`; // 设置透明背景
    }
  };

  /**
   * 产品点击
   * @param product 产品
   */
  const handleGoodsClick = (product: Goods) => {
    monkey.$router.navigateTo('/modules/goods/detail/index?id=' + product.id);
  };

  /**
   * 交易大厅点击
   */
  const handleTradingClick = () => {
    monkey.$router.navigateTo('/modules/home/<USER>/index');
  };

  /**
   * 产品列表点击
   */
  const handleGoodsListClick = () => {
    monkey.$router.navigateTo('/modules/goods/list/index');
  };

  /**
   * 药材行情点击
   */
  const handleMedicineClick = () => {
    monkey.$router.navigateTo('/modules/home/<USER>/index');
  };

  /**
   * 药材行情点击
   */
  const handlePriceClick = () => {
    monkey.$router.switchTab('/pages/medicine/index');
  };

  /**
   * 监听页面滚动
   * @param e 滚动事件
   */
  onPageScroll((e: Page.PageScrollOption) => {
    handlePageScroll(e);
  });

  onLoad(() => {});
</script>

<style lang="scss" scoped></style>
