<template>
  <ly-layout is-arc-bg :active="3">
    <!-- 顶部用户信息区域 -->
    <div class="header relative z-20 px-4 pb-5" :style="{ backgroundColor: navBarBgColor }">
      <ly-status-bar />
      <div class="mb-5 mt-8">
        <ui-user-card />
      </div>
    </div>
    <!-- 主要内容区域 -->
    <div class="flex-1 px-4 relative z-10 bg-gradient-to-br from-gray-50 to-blue-50 -mt-4 rounded-t-2xl">
      <!-- 认证状态卡片 -->
      <div class="mb-4 mt-5">
        <ui-user-verify />
      </div>
      <!-- 冷库业务概览 -->
      <div class="mb-4" v-if="false">
        <ui-user-bo @allOrders="handleAllOrders" />
      </div>

      <!-- 快捷功能 -->
      <div class="mb-4">
        <ui-user-quick />
      </div>

      <!-- 冷库监控中心 -->
      <div v-if="false" class="bg-white rounded-lg p-4 shadow-lg border border-gray-100 mb-4">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <div class="h-6 w-6 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg flex items-center justify-center mr-2">
              <div class="text-xs text-white">🌡️</div>
            </div>
            <div class="text-base font-bold text-gray-800">冷库监控</div>
          </div>
          <div class="flex items-center text-xs text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full">
            <div class="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-1 animate-pulse"></div>
            <span>实时监控</span>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-3">
          <div v-for="monitor in monitoringData" :key="monitor.name" class="p-3 bg-gradient-to-br from-gray-50 to-cyan-50 rounded-lg border border-gray-100">
            <div class="flex items-center justify-between mb-2">
              <div class="h-7 w-7 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center text-white text-xs">
                {{ monitor.icon }}
              </div>
              <div class="text-xs" :class="monitor.statusColor">
                {{ monitor.status }}
              </div>
            </div>
            <div class="font-medium text-gray-800 text-sm">
              {{ monitor.name }}
            </div>
            <div class="text-xs text-gray-500 mt-0.5">{{ monitor.value }}</div>
          </div>
        </div>
      </div>

      <!-- 仓储服务 -->
      <div v-if="false" class="bg-white rounded-lg p-4 shadow-lg border border-gray-100 mb-6">
        <div class="flex items-center mb-4">
          <div class="h-6 w-6 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center mr-2">
            <div class="text-xs text-white">📦</div>
          </div>
          <div class="text-base font-bold text-gray-800">仓储服务</div>
        </div>
        <div class="space-y-3">
          <div
            v-for="storage in storageServices"
            :key="storage.name"
            class="flex items-center p-3 bg-gradient-to-r from-gray-50 to-indigo-50 rounded-lg border border-gray-100 hover:shadow-md transition-all"
          >
            <div class="h-8 w-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center text-white mr-3 shadow-sm">
              <div class="text-xs">{{ storage.icon }}</div>
            </div>
            <div class="flex-1">
              <div class="font-medium text-gray-800 text-sm">
                {{ storage.name }}
              </div>
              <div class="text-xs text-gray-500">{{ storage.desc }}</div>
            </div>
            <div class="flex items-center">
              <div class="text-xs text-[#006dbb] bg-blue-50 px-2 py-0.5 rounded-full mr-2">
                {{ storage.status }}
              </div>
              <div class="text-[#006dbb] text-xs">→</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ly-line-bar height="150"></ly-line-bar>
  </ly-layout>
</template>

<script setup lang="ts">
  uni.hideTabBar();
  // 导入配置
  import monkey from '@/monkey';

  // 导航栏背景颜色
  const navBarBgColor: Ref<string> = ref('rgba(0, 109, 187, 0)');

  /**
   * 处理订单
   * @param type 订单类型
   */
  const handleAllOrders = ({ type, status }: { type: string; status: number }) => {
    monkey.$router.navigateTo(`/modules/order/${type}/index?status=${status}`);
  };

  // 月度统计
  const monthlyStats = reactive({
    purchase: '156',
    sales: '142',
  });

  // 冷库监控数据
  const monitoringData = reactive([
    {
      name: '1号冷库',
      icon: '🌡️',
      value: '-18°C | 65%RH',
      status: '正常',
      statusColor: 'text-emerald-600',
    },
    {
      name: '2号冷库',
      icon: '🌡️',
      value: '-20°C | 68%RH',
      status: '正常',
      statusColor: 'text-emerald-600',
    },
    {
      name: '制冷系统',
      icon: '❄️',
      value: '运行中',
      status: '良好',
      statusColor: 'text-blue-600',
    },
    {
      name: '电力系统',
      icon: '⚡',
      value: '380V 稳定',
      status: '正常',
      statusColor: 'text-emerald-600',
    },
  ]);

  // 仓储服务
  const storageServices = reactive([
    {
      name: '冷库监控',
      desc: '实时温度湿度监控',
      icon: '🌡️',
      status: '正常',
    },
    {
      name: '库存管理',
      desc: '货物进出库管理',
      icon: '📦',
      status: '运行中',
    },
    {
      name: '预约服务',
      desc: '入库出库预约',
      icon: '📅',
      status: '可用',
    },
    {
      name: '质检报告',
      desc: '货物质量检测',
      icon: '🔍',
      status: '最新',
    },
  ]);

  onShow(() => {
    monkey.$stores.useUserStore().getCurrentUserInfo();
  });
</script>

<style lang="scss" scoped></style>
