<script setup lang="ts">
  import monkey from '@/monkey';

  // 获取登录状态
  const { hasLogin } = storeToRefs(monkey.$stores.useUserStore());

  interface GoodsItem {
    id: string;
    name: string;
    quantity: number;
    unit: string;
    weight: number;
    warehouse: string;
    location: string;
    storageDate: string;
    expiryDate: string;
    status: 'normal' | 'expiring' | 'expired' | 'pledged';
  }

  const goodsList = ref<GoodsItem[]>([
    {
      id: '1',
      name: '当归',
      quantity: 500,
      unit: '件',
      weight: 500 * 50,
      warehouse: 'A区仓库',
      location: 'A-01-001',
      storageDate: '2024-01-15',
      expiryDate: '2025-01-15',
      status: 'normal',
    },
    {
      id: '2',
      name: '川芎',
      quantity: 300,
      unit: '件',
      weight: 300 * 50,
      warehouse: 'B区仓库',
      location: 'B-02-015',
      storageDate: '2024-02-20',
      expiryDate: '2024-12-20',
      status: 'expiring',
    },
    {
      id: '3',
      name: '白芍',
      quantity: 200,
      unit: '件',
      weight: 200 * 50,
      warehouse: 'A区仓库',
      location: 'A-03-008',
      storageDate: '2023-12-10',
      expiryDate: '2024-06-10',
      status: 'expired',
    },
    {
      id: '4',
      name: '人参',
      quantity: 150,
      unit: '件',
      weight: 150 * 30,
      warehouse: 'C区仓库',
      location: 'C-01-005',
      storageDate: '2024-03-10',
      expiryDate: '2026-03-10',
      status: 'pledged',
    },
    {
      id: '5',
      name: '枸杞',
      quantity: 800,
      unit: '件',
      weight: 800 * 25,
      warehouse: 'B区仓库',
      location: 'B-03-012',
      storageDate: '2024-04-05',
      expiryDate: '2025-04-05',
      status: 'normal',
    },
    {
      id: '6',
      name: '黄芪',
      quantity: 400,
      unit: '件',
      weight: 400 * 40,
      warehouse: 'A区仓库',
      location: 'A-02-018',
      storageDate: '2024-01-20',
      expiryDate: '2025-01-20',
      status: 'pledged',
    },
  ]);

  function getStatusText(status: string) {
    switch (status) {
      case 'normal':
        return '正常';
      case 'expiring':
        return '即将到期';
      case 'expired':
        return '已过期';
      case 'pledged':
        return '已质押';
      default:
        return '未知';
    }
  }

  function isExpiringSoon(expiryDate: string) {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30; // 30天内到期
  }

  function handleViewGoods(item: GoodsItem) {
    console.log('看货:', item);
    monkey.$router.navigateTo(`/modules/home/<USER>/look?id=${item.id}`);
  }

  function handleOutbound(item: GoodsItem) {
    console.log('出库:', item);
    monkey.$helper.toast.success('申请出库');
  }

  function handlePledge(item: GoodsItem) {
    console.log('仓库质押:', item);
    monkey.$helper.toast.success('申请质押');
  }

  function handleRenewal(item: GoodsItem) {
    console.log('便捷续费:', item);
    monkey.$helper.toast.success('续费申请已提交');
  }

  function handleShipping(item: GoodsItem) {
    console.log('货运服务:', item);
    monkey.$helper.toast.success('货运服务申请已提交');
  }

  function handleGlobalShipping() {
    console.log('全局货运服务');
    monkey.$router.navigateTo(`/modules/warehouse/freight/index`);
  }

  function handleGlobalPayment() {
    console.log('便捷缴费');
    monkey.$helper.toast.success('缴费页面即将开放');
  }

  function goDetail(item: GoodsItem) {
    monkey.$router.navigateTo(`/modules/warehouse/detail/index?id=${item.id}`);
  }

  const handleLogin = () => {
    monkey.$stores.useAuthModalStore().open();
  };

  onLoad(() => {
    console.log('我的仓库页面加载');
  });
</script>

<template>
  <ly-layout :isArcBg="!!hasLogin" :active="1">
    <block v-if="hasLogin">
      <!-- 顶部功能按钮 -->
      <div class="relative z-10 px-4 py-4">
        <div class="grid grid-cols-2 gap-3">
          <!-- 便捷缴费按钮 -->
          <div
            class="group flex items-center gap-3 rounded-lg bg-gradient-to-r from-green-25 to-green-50 px-4 py-3.5 shadow-sm border border-green-100 transition-all duration-300 hover:shadow-md active:scale-95 active:shadow-lg"
            @click="handleGlobalPayment"
          >
            <div class="flex size-8 items-center justify-center rounded-lg bg-green-400 shadow-sm group-active:scale-95 transition-transform duration-200">
              <i class="i-mdi-credit-card text-white text-base" />
            </div>
            <div class="flex flex-col flex-1">
              <span class="text-sm font-semibold text-green-600">便捷缴费</span>
              <span class="text-xs text-green-500/70">在线支付</span>
            </div>
            <div class="i-mdi-chevron-right text-green-300 text-lg opacity-60 group-active:opacity-100 transition-opacity duration-200" />
          </div>
          <!-- 货运服务按钮 -->
          <div
            class="group flex items-center gap-3 rounded-lg bg-gradient-to-r from-blue-25 to-blue-50 px-4 py-3.5 shadow-sm border border-blue-100 transition-all duration-300 hover:shadow-md active:scale-95 active:shadow-lg"
            @click="handleGlobalShipping"
          >
            <div class="flex size-8 items-center justify-center rounded-lg bg-blue-400 shadow-sm group-active:scale-95 transition-transform duration-200">
              <i class="i-mdi-truck-delivery text-white text-base" />
            </div>
            <div class="flex flex-col flex-1">
              <span class="text-sm font-semibold text-blue-600">货运服务</span>
              <span class="text-xs text-blue-500/70">物流运输</span>
            </div>
            <div class="i-mdi-chevron-right text-blue-300 text-lg opacity-60 group-active:opacity-100 transition-opacity duration-200" />
          </div>
        </div>
      </div>

      <!-- 货物列表 -->
      <div class="relative z-10 space-y-3 px-4 pb-4">
        <div v-if="goodsList.length > 0" class="space-y-3">
          <div
            v-for="item in goodsList"
            :key="item.id"
            class="overflow-hidden rounded-lg border shadow-md transition-shadow duration-200 active:shadow-md"
            :class="[item.status === 'pledged' ? 'border-gray-100 bg-white' : 'border-gray-100 bg-white']"
          >
            <div class="relative p-4" @click="goDetail(item)">
              <!-- 货物名称和状态 -->
              <div class="mb-3 flex items-center justify-between">
                <h3 class="flex-1 text-lg font-semibold text-gray-800">
                  {{ item.name }}
                </h3>
                <div class="flex items-center gap-2">
                  <!-- 服务功能快捷入口 -->
                  <div v-if="item.status !== 'pledged'" class="flex gap-1">
                    <div class="flex items-center justify-center rounded-full bg-blue-100 p-1 text-blue-600 transition-colors duration-200 active:scale-95" @click.stop="handleRenewal(item)" title="续费">
                      <i class="i-line-md-calendar-plus text-xs" />
                    </div>
                    <div class="flex items-center justify-center rounded-full bg-green-100 p-1 text-green-600 transition-colors duration-200 active:scale-95" @click.stop="handleShipping(item)" title="货运">
                      <i class="i-line-md-shipping-box text-xs" />
                    </div>
                  </div>
                  <span
                    class="rounded-full px-2 py-1 text-xs font-medium"
                    :class="[
                      item.status === 'normal' ? 'bg-theme-green/10 text-theme-green' : item.status === 'expiring' ? 'bg-yellow-100 text-yellow-600' : item.status === 'expired' ? 'bg-red-100 text-red-600' : 'bg-gray-200 text-gray-600',
                    ]"
                  >
                    {{ getStatusText(item.status) }}
                  </span>
                </div>
              </div>

              <!-- 货物详细信息网格 -->
              <div class="mb-4 grid grid-cols-2 gap-3">
                <div class="flex items-center space-x-2">
                  <div class="size-2 rounded-full bg-theme-blue" />
                  <div class="text-sm text-gray-600">数量：</div>
                  <div class="text-sm font-medium text-gray-800">{{ item.quantity }} {{ item.unit }}</div>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="size-2 rounded-full bg-theme-green" />
                  <div class="text-sm text-gray-600">重量：</div>
                  <div class="text-sm font-medium text-gray-800">{{ item.weight }} kg</div>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="size-2 rounded-full bg-yellow-500" />
                  <div class="text-sm text-gray-600">仓库：</div>
                  <div class="text-sm font-medium text-gray-800">
                    {{ item.warehouse }}
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="size-2 rounded-full bg-purple-500" />
                  <div class="text-sm text-gray-600">货位：</div>
                  <div class="text-sm font-medium text-gray-800">
                    {{ item.location }}
                  </div>
                </div>
              </div>

              <!-- 日期信息和服务功能 -->
              <div class="mb-4 grid grid-cols-2 gap-3">
                <div class="rounded-lg bg-gray-50 p-3">
                  <div class="mb-1 text-xs text-gray-500">存入日期</div>
                  <div class="text-sm font-medium text-gray-800">
                    {{ item.storageDate }}
                  </div>
                </div>
                <div class="rounded-lg bg-gray-50 p-3">
                  <div class="mb-1 text-xs text-gray-500">到期日期</div>
                  <div class="text-sm font-medium" :class="isExpiringSoon(item.expiryDate) ? 'text-red-600' : 'text-gray-800'">
                    {{ item.expiryDate }}
                  </div>
                </div>

                <!-- 服务功能按钮 -->
                <!-- <template v-if="item.status !== 'pledged'">
                <div class="rounded-lg bg-gray-50 p-3">
                  <div
                    class="flex items-center justify-center gap-1.5 rounded-md bg-white px-3 py-2 text-xs font-medium text-red-600 shadow-sm transition-all duration-200 active:scale-95 hover:shadow-md border border-red-200"
                    @click.stop="handleRenewal(item)"
                  >
                    <i class="i-mdi-cash-clock text-sm" />
                    <span>便捷续费</span>
                  </div>
                </div>
                <div class="rounded-lg bg-gray-50 p-3">
                  <div
                    class="flex items-center justify-center gap-1.5 rounded-md bg-white px-3 py-2 text-xs font-medium text-blue-600 shadow-sm transition-all duration-200 active:scale-95 hover:shadow-md border border-blue-200"
                    @click.stop="handleShipping(item)"
                  >
                    <i class="i-mdi-bus-clock text-sm" />
                    <span>货运服务</span>
                  </div>
                </div>
              </template> -->
              </div>

              <!-- 质押状态覆盖层 -->
              <div v-if="item.status === 'pledged'" class="absolute inset-0 z-10 overflow-hidden rounded-t-lg">
                <!-- 轻柔遮罩 -->
                <div class="absolute inset-0 bg-amber-50/60 backdrop-blur-[0.5px]" />

                <!-- 质押印章 -->
                <div class="absolute right-2 top-2 rotate-12">
                  <div class="flex size-16 items-center justify-center rounded-full border-2 border-amber-500 bg-white shadow-sm">
                    <div class="flex flex-col gap-0.5 text-center text-xs font-bold leading-tight text-amber-600">
                      <div>已</div>
                      <div>质</div>
                      <div>押</div>
                    </div>
                  </div>
                </div>

                <!-- 简洁状态信息 -->
                <div class="absolute inset-x-6 top-1/2 -translate-y-1/2">
                  <div class="flex flex-col items-center text-center gap-2">
                    <div class="i-mdi-timer-lock text-amber-500/70 text-2xl" />
                    <div class="text-xs text-amber-600/80">货物已质押，库存冻结</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="border-t border-gray-100 bg-gray-50 px-4 py-3">
              <div class="flex justify-end gap-2">
                <div
                  class="flex items-center justify-center rounded-full bg-theme-blue px-4 py-2 text-xs tracking-wider text-white transition-colors duration-200 active:scale-95 active:bg-theme-blue-600"
                  @click.stop="handleViewGoods(item)"
                >
                  <i class="i-mdi-image-sync-outline mr-1" />
                  实况
                </div>
                <div
                  v-if="item.status !== 'pledged' && item.status !== 'expired'"
                  class="flex items-center justify-center rounded-full bg-theme-green px-4 py-2 text-xs tracking-wider text-white transition-colors duration-200 active:scale-95 active:bg-theme-green-600"
                  @click.stop="handleOutbound(item)"
                >
                  <i class="i-mdi-arrow-up-bold-circle-outline mr-1" />
                  出库
                </div>
                <div
                  v-if="item.status !== 'pledged' && item.status !== 'expired'"
                  class="flex items-center justify-center rounded-full bg-gray-600 px-4 py-2 text-xs tracking-wider text-white transition-colors duration-200 active:scale-95 active:bg-gray-700"
                  @click.stop="handlePledge(item)"
                >
                  <i class="i-mdi-timer-lock-outline mr-1" />
                  质押
                </div>
                <div
                  v-if="item.status === 'pledged'"
                  class="flex items-center justify-center rounded-full bg-yellow-500 px-4 py-2 text-xs tracking-wider text-white transition-colors duration-200 active:scale-95 active:bg-yellow-700"
                  @click.stop="handlePledge(item)"
                >
                  <i class="i-mdi-timer-lock-open-outline mr-1" />
                  解押
                </div>
                <div
                  v-if="item.status !== 'pledged'"
                  class="flex items-center justify-center rounded-full bg-red-500 px-4 py-2 text-xs tracking-wider text-white transition-colors duration-200 active:scale-95 active:bg-red-700"
                  @click.stop="handlePledge(item)"
                >
                  <i class="i-mdi-credit-card-outline mr-1" />
                  缴费
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="rounded-xl bg-white p-8 text-center shadow-sm">
          <div class="mb-3 text-gray-400">
            <i class="i-mdi-warehouse text-6xl" />
          </div>
          <div class="mb-2 text-base text-gray-500">暂无货物</div>
          <div class="text-sm text-gray-400">您的仓库中还没有存储任何货物</div>
        </div>
      </div>
      <ly-line-bar height="150" />
    </block>
    <div v-else class="relative z-10 flex flex-col items-center justify-center h-screen">
      <div class="absolute top-1/2 -translate-y-2/3">
        <van-empty image="error" description="请先登录">
          <div class="text-sm text-gray-500 mb-4">登录后可查看您的仓库</div>
          <div
            class="flex items-center justify-center rounded-full bg-theme-blue px-6 py-2.5 text-sm font-medium text-white shadow-md transition-all duration-300 hover:shadow-lg active:scale-95 active:bg-theme-blue-600"
            @click="handleLogin"
          >
            <i class="i-mdi-login mr-1.5" />
            去登录
          </div>
        </van-empty>
      </div>
    </div>
  </ly-layout>
</template>
