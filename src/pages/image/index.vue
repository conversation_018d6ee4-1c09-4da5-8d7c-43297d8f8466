<template>
  <ly-layout :active="2">
    <!-- 顶部导航区域 -->
    <div class="sticky top-0 z-10 bg-white/95 backdrop-blur-sm border-b border-gray-100">
      <div class="flex items-center justify-between px-4 py-2.5 gap-2">
        <!-- 搜索框 -->
        <div class="flex-1">
          <uni-search-bar padding="0" v-model="searchQuery" placeholder="搜索社区内容..." @search="handleSearch" @clear="clearSearch" @cancel="clearSearch" radius="20" cancel-button="none" :focus="false" />
        </div>

        <!-- 发布按钮 -->
        <div
          @click="showCreatePost"
          class="flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-sm rounded-full active:from-blue-600 active:to-blue-700 transition-all duration-300 shadow-lg shadow-blue-200/50"
        >
          <div class="i-mdi-plus text-lg"></div>
          发布
        </div>
      </div>

      <!-- 筛选标签 
      <div class="px-4 pb-3">
        <div class="flex space-x-2 overflow-x-auto scrollbar-hide">
          <div 
            v-for="tag in filterTags" 
            :key="tag.key"
            @click="selectFilter(tag.key)"
            class="flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-300"
            :class="activeFilter === tag.key 
              ? 'bg-blue-500 text-white shadow-lg shadow-blue-200/50' 
              : 'bg-gray-100 text-gray-600 active:bg-gray-200'"
          >
            {{ tag.label }}
          </div>
        </div>
      </div>-->
    </div>

    <!-- 社区信息列表 -->
    <div class="px-4 py-2">
      <!-- 骨架屏加载状态 -->
      <div v-if="loading && posts.length === 0" class="space-y-4">
        <div v-for="i in 3" :key="i" class="bg-white rounded-2xl p-4 shadow-sm animate-pulse">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-12 h-12 bg-gray-200 rounded-full"></div>
            <div class="flex-1">
              <div class="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div class="h-3 bg-gray-200 rounded w-1/4"></div>
            </div>
          </div>
          <div class="h-20 bg-gray-200 rounded-xl mb-4"></div>
          <div class="flex space-x-4">
            <div class="h-8 bg-gray-200 rounded w-16"></div>
            <div class="h-8 bg-gray-200 rounded w-16"></div>
            <div class="h-8 bg-gray-200 rounded w-16"></div>
          </div>
        </div>
      </div>

      <!-- 社区信息卡片列表 -->
      <div v-else-if="posts.length > 0" class="space-y-4">
        <ui-image-card v-for="post in posts" :key="post.id" :post="post" @like="handleLike" @comment="handleComment" @share="handleShare" @bookmark="handleBookmark" @preview-images="handlePreviewImages" />
      </div>

      <!-- 空状态 -->
      <div v-else class="flex flex-col items-center justify-center py-20">
        <div class="w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-6">
          <div class="i-mdi-image-multiple-outline text-4xl text-gray-400"></div>
        </div>
        <text class="text-lg font-medium text-gray-600 mb-2">暂无社区内容</text>
        <p class="text-sm text-gray-400 mb-6 text-center">快来发布第一条社区信息吧</p>
        <div @click="showCreatePost" class="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full active:from-blue-600 active:to-blue-700 transition-all duration-300 shadow-lg shadow-blue-200/50">立即发布</div>
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore && posts.length > 0" class="flex justify-center py-6">
        <div @click="loadMore" :disabled="loadingMore" class="px-6 py-3 bg-white border border-gray-200 text-gray-600 rounded-full active:bg-gray-50 transition-colors duration-300 disabled:opacity-50">
          <div v-if="loadingMore" class="i-mdi-loading animate-spin mr-2"></div>
          {{ loadingMore ? '加载中...' : '加载更多' }}
        </div>
      </div>
    </div>

    <!-- 评论弹窗 -->
    <van-popup v-model:show="showCommentModal" position="bottom" :style="{ height: '70vh' }" round closeable close-icon="close">
      <div class="h-full flex flex-col">
        <!-- 评论标题 -->
        <div class="flex items-center justify-between px-4 py-3 border-b border-gray-100">
          <text class="text-lg font-semibold text-gray-900">评论 {{ comments.length }}</text>
          <div @click="showCommentModal = false" class="i-mdi-close text-gray-400 text-xl"></div>
        </div>

        <!-- 评论列表 -->
        <div class="flex-1 overflow-y-auto px-4">
          <div v-if="comments.length === 0" class="flex flex-col items-center justify-center h-full">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <div class="i-mdi-comment-outline text-2xl text-gray-400"></div>
            </div>
            <p class="text-gray-500 text-sm">还没有评论，快来抢沙发吧~</p>
          </div>
          <div v-else class="divide-y divide-gray-100">
            <ly-comment-item v-for="comment in comments" :key="comment.id" :comment="comment" @like="handleCommentLike" @reply="handleCommentReply" />
          </div>
        </div>

        <!-- 评论输入框 -->
        <div class="px-4 py-3 border-t border-gray-100 bg-white">
          <div class="flex space-x-3">
            <input
              v-model="commentText"
              type="text"
              placeholder="写下你的评论..."
              class="flex-1 px-4 py-3 bg-gray-50 border border-gray-200 rounded-full focus:outline-none focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-300"
              @keyup.enter="submitComment"
            />
            <div @click="submitComment" :disabled="!commentText.trim()" class="px-6 py-3 bg-blue-500 text-white rounded-full active:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300">发送</div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 图片预览 -->
    <ly-image-preview 
      v-model:show="showImagePreview" 
      :images="previewImages" 
      :current="currentImageIndex"
      @change="onImageChange"
    />
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import type { ImagePost, Comment } from '@/monkey/types/image';
  import type { UserProfile } from '@/monkey/types/user';

  // 响应式数据
  const loading = ref(true);
  const loadingMore = ref(false);
  const hasMore = ref(true);
  const searchQuery = ref('');
  const activeFilter = ref('all');
  const posts = ref<ImagePost[]>([]);
  const comments = ref<Comment[]>([]);
  const showCommentModal = ref(false);
  const commentText = ref('');
  const currentPostId = ref('');
  const showImagePreview = ref(false);
  const previewImages = ref<string[]>([]);
  const currentImageIndex = ref(0);

  // 筛选标签
  const filterTags = [
    { key: 'all', label: '全部' },
    { key: 'hot', label: '热门' },
    { key: 'new', label: '最新' },
    { key: 'following', label: '关注' },
    { key: 'nearby', label: '附近' },
  ];

  // 模拟用户数据
  const mockUser: UserProfile = {
    id: '1',
    nc: '小明',
    tx: 'https://picsum.photos/100/100?random=1',
    zsxm: '张小明',
    sjh: '13800138000',
    createTime: '2024-01-01T00:00:00Z',
    updateTime: '2024-01-01T00:00:00Z',
    sfrz: 1,
  };

  // 生成模拟数据
  const generateMockPosts = (): ImagePost[] => {
    const posts: ImagePost[] = [];
    for (let i = 1; i <= 10; i++) {
      posts.push({
        id: `post_${i}`,
        user: {
          ...mockUser,
          id: `user_${i}`,
          nc: `用户${i}`,
          tx: `https://picsum.photos/100/100?random=${i}`,
          sfrz: Math.random() > 0.5 ? 1 : 0,
        },
        content: `这是第${i}条社区内容，分享生活中的美好瞬间。今天天气很好，心情也很不错！#生活 #分享 #美好`,
        images: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, j) => ({
          id: `img_${i}_${j}`,
          name: `image_${j}.jpg`,
          url: `https://picsum.photos/400/300?random=${i * 10 + j}`,
          fileSize: 1024000,
          mimeType: 'image/jpeg',
          fileExtension: 'jpg',
          createdTime: new Date().toISOString(),
          modifiedTime: new Date().toISOString(),
          createdBy: `user_${i}`,
          modifiedBy: `user_${i}`,
          remarks: '',
          schemaCode: '',
          refId: '',
          bizObjectId: '',
          bizPropertyCode: '',
          deleted: false,
        })),
        likeCount: Math.floor(Math.random() * 1000),
        commentCount: Math.floor(Math.random() * 100),
        isLiked: Math.random() > 0.5,
        createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        tags: ['生活', '分享', '美好'],
        location: i % 3 === 0 ? '北京市朝阳区' : undefined,
      });
    }
    return posts;
  };

  // 页面加载
  onMounted(async () => {
    await loadPosts();
  });

  // 加载帖子数据
  const loadPosts = async () => {
    loading.value = true;

    // 模拟API请求延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    posts.value = generateMockPosts();
    loading.value = false;
  };

  // 处理搜索
  const handleSearch = (e?: any) => {
    const keyword = e?.value || searchQuery.value;
    console.log('搜索:', keyword);
    // 这里可以实现搜索逻辑
    if (keyword.trim()) {
      uni.showToast({
        title: `搜索: ${keyword}`,
        icon: 'none',
      });
    }
  };

  // 清除搜索
  const clearSearch = () => {
    searchQuery.value = '';
    console.log('清除搜索');
  };

  // 选择筛选
  const selectFilter = (filterKey: string) => {
    activeFilter.value = filterKey;
    console.log('筛选:', filterKey);
    // 这里可以实现筛选逻辑
  };

  // 显示创建帖子
  const showCreatePost = () => {
    monkey.$router.navigateTo('/modules/image/editor/index');
  };

  // 处理点赞
  const handleLike = (postId: string) => {
    const post = posts.value.find((p) => p.id === postId);
    if (post) {
      post.isLiked = !post.isLiked;
      post.likeCount += post.isLiked ? 1 : -1;
    }
  };

  // 处理评论
  const handleComment = (postId: string) => {
    currentPostId.value = postId;
    loadComments(postId);
    showCommentModal.value = true;
  };

  // 处理分享
  const handleShare = (postId: string) => {
    uni.showShareMenu({
      success: () => {
        console.log('分享成功');
      },
    });
  };

  // 处理收藏
  const handleBookmark = (postId: string) => {
    uni.showToast({
      title: '已收藏',
      icon: 'success',
    });
  };

  // 处理图片预览
  const handlePreviewImages = (images: string[], current: number) => {
    previewImages.value = images;
    currentImageIndex.value = current;
    showImagePreview.value = true;
  };

  // 图片切换
  const onImageChange = (e: any) => {
    currentImageIndex.value = e.detail.current;
  };

  // 加载评论
  const loadComments = async (postId: string) => {
    // 模拟评论数据
    comments.value = [
      {
        id: 'comment_1',
        user: mockUser,
        content: '很棒的分享！',
        likeCount: 5,
        isLiked: false,
        createTime: new Date().toISOString(),
      },
      {
        id: 'comment_2',
        user: { ...mockUser, id: 'user_2', nc: '小红', tx: 'https://picsum.photos/100/100?random=2' },
        content: '确实很美，我也想去！',
        likeCount: 3,
        isLiked: true,
        createTime: new Date(Date.now() - 60000).toISOString(),
        replyToId: 'comment_1',
        replyToUser: '小明',
      },
    ];
  };

  // 提交评论
  const submitComment = () => {
    if (!commentText.value.trim()) return;

    const newComment: Comment = {
      id: `comment_${Date.now()}`,
      user: mockUser,
      content: commentText.value.trim(),
      likeCount: 0,
      isLiked: false,
      createTime: new Date().toISOString(),
    };

    comments.value.push(newComment);
    commentText.value = '';

    // 更新帖子评论数
    const post = posts.value.find((p) => p.id === currentPostId.value);
    if (post) {
      post.commentCount += 1;
    }
  };

  // 处理评论点赞
  const handleCommentLike = (commentId: string) => {
    const comment = comments.value.find((c) => c.id === commentId);
    if (comment) {
      comment.isLiked = !comment.isLiked;
      comment.likeCount += comment.isLiked ? 1 : -1;
    }
  };

  // 处理评论回复
  const handleCommentReply = (commentId: string, content: string) => {
    const parentComment = comments.value.find((c) => c.id === commentId);
    if (!parentComment) return;

    const replyComment: Comment = {
      id: `reply_${Date.now()}`,
      user: mockUser,
      content: content,
      likeCount: 0,
      isLiked: false,
      createTime: new Date().toISOString(),
      replyToId: commentId,
      replyToUser: parentComment.user.zsxm || parentComment.user.nc,
    };

    comments.value.push(replyComment);
  };

  // 加载更多
  const loadMore = async () => {
    if (loadingMore.value || !hasMore.value) return;

    loadingMore.value = true;

    // 模拟加载延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const newPosts = generateMockPosts();
    posts.value.push(...newPosts);

    // 模拟没有更多数据
    if (posts.value.length >= 30) {
      hasMore.value = false;
    }

    loadingMore.value = false;
  };
</script>

<style scoped lang="scss">
  :deep(.uni-searchbar) {
    padding: 0 !important;
  }

  // 隐藏滚动条
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  // 搜索框动画
  input:focus {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
  }

  // 按钮悬停效果
  div:active {
    transform: translateY(-1px);
  }

  // 骨架屏动画
  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .animate-pulse {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }
</style>
