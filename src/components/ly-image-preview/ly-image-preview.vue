<template>
  <van-popup 
    v-model:show="visible" 
    overlay-style="background: rgba(0, 0, 0, 0.95)"
    :close-on-click-overlay="true"
    :duration="300"
    @close="handleClose"
  >
    <div class="w-screen h-screen relative overflow-hidden">
      <!-- 关闭按钮 -->
      <button 
        @click="handleClose"
        class="absolute top-4 right-4 z-20 w-10 h-10 bg-black/40 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/60 transition-colors duration-300"
      >
        <div class="i-mdi-close text-xl"></div>
      </button>

      <!-- 下载按钮 -->
      <button 
        @click="downloadImage"
        class="absolute top-4 right-16 z-20 w-10 h-10 bg-black/40 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/60 transition-colors duration-300"
      >
        <div class="i-mdi-download text-lg"></div>
      </button>

      <!-- 图片计数器 -->
      <div v-if="images.length > 1" class="absolute top-4 left-1/2 transform -translate-x-1/2 z-20 bg-black/40 backdrop-blur-sm px-4 py-2 rounded-full">
        <span class="text-white text-sm font-medium">{{ currentIndex + 1 }} / {{ images.length }}</span>
      </div>

      <!-- 图片轮播 -->
      <swiper 
        :current="currentIndex"
        @change="onImageChange"
        :indicator-dots="false"
        :autoplay="false"
        :circular="false"
        class="w-full h-full"
      >
        <swiper-item v-for="(image, index) in images" :key="index" class="flex items-center justify-center p-4">
          <div 
            class="relative max-w-full max-h-full flex items-center justify-center"
            @click="handleImageClick"
          >
            <!-- 加载状态 -->
            <div v-if="loadingStates[index]" class="absolute inset-0 flex items-center justify-center">
              <div class="w-12 h-12 border-4 border-white/20 border-t-white rounded-full animate-spin"></div>
            </div>
            
            <!-- 图片 -->
            <image 
              :src="image" 
              mode="aspectFit"
              class="max-w-full max-h-full transition-all duration-300"
              :class="{ 'opacity-0': loadingStates[index] }"
              @load="onImageLoad(index)"
              @error="onImageError(index)"
              @click.stop="handleImageClick"
            />
          </div>
        </swiper-item>
      </swiper>

      <!-- 左右切换按钮 -->
      <div v-if="images.length > 1" class="absolute inset-y-0 left-0 flex items-center">
        <button 
          @click="prevImage"
          :disabled="currentIndex === 0"
          class="ml-4 w-12 h-12 bg-black/40 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/60 transition-colors duration-300 disabled:opacity-30 disabled:cursor-not-allowed"
        >
          <div class="i-mdi-chevron-left text-2xl"></div>
        </button>
      </div>

      <div v-if="images.length > 1" class="absolute inset-y-0 right-0 flex items-center">
        <button 
          @click="nextImage"
          :disabled="currentIndex === images.length - 1"
          class="mr-4 w-12 h-12 bg-black/40 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/60 transition-colors duration-300 disabled:opacity-30 disabled:cursor-not-allowed"
        >
          <div class="i-mdi-chevron-right text-2xl"></div>
        </button>
      </div>

      <!-- 缩略图 -->
      <div v-if="images.length > 1" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
        <div class="flex space-x-2 bg-black/40 backdrop-blur-sm rounded-full px-4 py-2 max-w-sm overflow-x-auto scrollbar-hide">
          <button
            v-for="(image, index) in images"
            :key="index"
            @click="goToImage(index)"
            class="flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 transition-all duration-300"
            :class="currentIndex === index ? 'border-white scale-110' : 'border-transparent opacity-60 hover:opacity-100'"
          >
            <image 
              :src="image" 
              mode="aspectFill"
              class="w-full h-full"
            />
          </button>
        </div>
      </div>

      <!-- 手势提示 -->
      <div v-if="showGestureHint" class="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-20 bg-black/60 backdrop-blur-sm px-4 py-2 rounded-full">
        <span class="text-white text-xs">👆 点击图片或左右滑动浏览</span>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
interface Props {
  show: boolean;
  images: string[];
  current?: number;
}

interface Emits {
  (e: 'update:show', value: boolean): void;
  (e: 'change', index: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  current: 0
});

const emit = defineEmits<Emits>();

// 响应式数据
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

const currentIndex = ref(props.current);
const loadingStates = ref<Record<number, boolean>>({});
const showGestureHint = ref(true);

// 监听初始索引变化
watch(() => props.current, (newIndex) => {
  currentIndex.value = newIndex;
});

// 监听显示状态
watch(visible, (show) => {
  if (show) {
    currentIndex.value = props.current;
    // 重置加载状态
    loadingStates.value = {};
    props.images.forEach((_, index) => {
      loadingStates.value[index] = true;
    });
    // 显示手势提示
    showGestureHint.value = true;
    setTimeout(() => {
      showGestureHint.value = false;
    }, 3000);
  }
});

// 图片切换
const onImageChange = (e: any) => {
  currentIndex.value = e.detail.current;
  emit('change', currentIndex.value);
};

// 图片加载完成
const onImageLoad = (index: number) => {
  loadingStates.value[index] = false;
};

// 图片加载失败
const onImageError = (index: number) => {
  loadingStates.value[index] = false;
  uni.showToast({
    title: '图片加载失败',
    icon: 'none'
  });
};

// 上一张图片
const prevImage = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
    emit('change', currentIndex.value);
  }
};

// 下一张图片
const nextImage = () => {
  if (currentIndex.value < props.images.length - 1) {
    currentIndex.value++;
    emit('change', currentIndex.value);
  }
};

// 跳转到指定图片
const goToImage = (index: number) => {
  currentIndex.value = index;
  emit('change', currentIndex.value);
};

// 处理图片点击
const handleImageClick = () => {
  // 可以在这里添加双击放大等功能
  console.log('图片点击');
};

// 关闭预览
const handleClose = () => {
  visible.value = false;
};

// 下载图片
const downloadImage = () => {
  const currentImageUrl = props.images[currentIndex.value];
  if (!currentImageUrl) return;
  
  uni.downloadFile({
    url: currentImageUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            });
          },
          fail: () => {
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
      }
    },
    fail: () => {
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      });
    }
  });
};

// 键盘事件处理
onMounted(() => {
  const handleKeydown = (e: KeyboardEvent) => {
    if (!visible.value) return;
    
    switch (e.key) {
      case 'ArrowLeft':
        prevImage();
        break;
      case 'ArrowRight':
        nextImage();
        break;
      case 'Escape':
        handleClose();
        break;
    }
  };
  
  document.addEventListener('keydown', handleKeydown);
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
  });
});
</script>

<style scoped lang="scss">
// 隐藏滚动条
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 按钮悬停效果
button:hover {
  transform: scale(1.05);
}

// 缩略图动画
.flex-shrink-0 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 图片加载动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

image {
  animation: fadeIn 0.3s ease-out;
}
</style> 