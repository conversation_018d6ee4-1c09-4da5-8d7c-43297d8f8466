<template>
  <div class="flex flex-col items-center justify-center pb-8" :class="containerClass">
    <!-- 图片显示区域 -->
    <div v-if="image" class="mb-4" :style="imageStyle">
      <image :src="image" :mode="imageMode" class="w-full h-full" />
    </div>

    <!-- 图标显示区域 -->
    <div v-else-if="icon" class="mb-2" :class="iconClass" :style="iconStyle">
      <text :class="icon"></text>
    </div>

    <!-- 默认图标 -->
    <div v-else class="mb-2 text-gray-400" :style="defaultIconStyle">
      <text class="i-mdi-inbox-outline"></text>
    </div>

    <!-- 主标题 -->
    <div v-if="title" class="font-bold mb-2" :class="titleClass" :style="titleStyle">
      {{ title }}
    </div>

    <!-- 描述文字 -->
    <div v-if="description" class="text-center leading-relaxed" :class="descriptionClass" :style="descriptionStyle">
      {{ description }}
    </div>

    <!-- 自定义内容插槽 -->
    <div v-if="$slots.default" class="mt-4">
      <slot></slot>
    </div>

    <!-- 操作按钮插槽 -->
    <div v-if="$slots.action" class="mt-6">
      <slot name="action"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    // 图片相关
    image?: string;
    imageWidth?: string | number;
    imageHeight?: string | number;
    imageMode?: 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix';

    // 图标相关
    icon?: string;
    iconSize?: string | number;
    iconColor?: string;

    // 文字相关
    title?: string;
    titleSize?: string | number;
    titleColor?: string;

    description?: string;
    descriptionSize?: string | number;
    descriptionColor?: string;

    // 容器相关
    containerClass?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    imageMode: 'aspectFit',
    imageWidth: 160,
    imageHeight: 160,
    iconSize: 100,
    iconColor: '#d1d5db',
    titleSize: 18,
    titleColor: '#374151',
    descriptionSize: 14,
    descriptionColor: '#6b7280',
    title: '暂无数据',
    description: '暂时没有相关数据',
  });

  // 计算样式
  const imageStyle = computed(() => ({
    width: typeof props.imageWidth === 'number' ? `${props.imageWidth}px` : props.imageWidth,
    height: typeof props.imageHeight === 'number' ? `${props.imageHeight}px` : props.imageHeight,
  }));

  const iconClass = computed(() => ['flex items-center justify-center']);

  const iconStyle = computed(() => ({
    fontSize: typeof props.iconSize === 'number' ? `${props.iconSize}px` : props.iconSize,
    color: props.iconColor,
  }));

  const defaultIconStyle = computed(() => ({
    fontSize: typeof props.iconSize === 'number' ? `${props.iconSize}px` : props.iconSize,
  }));

  const titleClass = computed(() => ['text-gray-800']);

  const titleStyle = computed(() => ({
    fontSize: typeof props.titleSize === 'number' ? `${props.titleSize}px` : props.titleSize,
    color: props.titleColor,
  }));

  const descriptionClass = computed(() => ['text-gray-500 max-w-xs px-4']);

  const descriptionStyle = computed(() => ({
    fontSize: typeof props.descriptionSize === 'number' ? `${props.descriptionSize}px` : props.descriptionSize,
    color: props.descriptionColor,
  }));
</script>
