<template>
  <div class="flex flex-col items-center justify-center h-750">
    <van-empty :image="image" :description="description">
      <slot />
    </van-empty>
  </div>
</template>

<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      image: 'error' | 'network' | 'search' | 'default';
      description: string;
    }>(),
    {
      image: 'error',
      description: '',
    },
  );
</script>
