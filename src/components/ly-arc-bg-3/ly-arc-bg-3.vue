<template>
  <div class="arc-background"></div>
</template>
<style scoped lang="scss">
  .arc-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 540rpx;
    background: linear-gradient(to bottom, rgb(16, 185, 129), rgb(16, 185, 129) 20%, rgba(16, 185, 129, 0.95) 50%, rgba(34, 197, 94, 0.5) 80%, rgba(22, 163, 74, 0) 100%);
    z-index: 0;
  }

  .arc-background::after {
    content: '';
    position: absolute;
    bottom: -40px;
    left: 0;
    width: 100%;
    height: 80px;
    background: radial-gradient(ellipse at center, rgba(34, 197, 94, 0.2) 0%, rgba(22, 163, 74, 0) 70%);
    border-radius: 50% 50% 0 0 / 100% 100% 0 0;
  }
</style>
