<template>
  <transition name="back-top-fade">
    <view
      v-if="showBackTop"
      class="fixed bottom-24 right-5 w-10 h-10 bg-gradient-to-br from-theme-blue via-theme-blue-light to-theme-blue rounded-full flex items-center justify-center cursor-pointer z-[1000] shadow-2xl border-2 border-white/30 backdrop-blur-md transition-all duration-300 overflow-hidden group hover:shadow-3xl hover:scale-105 active:scale-95 active:shadow-lg"
      style="box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 4px 10px rgba(59, 130, 246, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3), inset 0 -1px 0 rgba(0, 0, 0, 0.2);"
      @click="backToTop"
    >
      <text class="i-mdi-arrow-up-bold-circle-outline text-2xl text-white font-bold" style="text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.3);"></text>
      <!-- 涟漪效果 -->
      <view class="absolute inset-0 rounded-full bg-gradient-to-r from-white/40 via-white/20 to-transparent opacity-0 scale-0 group-active:opacity-100 group-active:scale-150 transition-all duration-500 ease-out" style="box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.3);"></view>
      <!-- 呼吸灯效果 -->
      <view class="absolute inset-0 rounded-full bg-gradient-to-br from-theme-blue/30 to-theme-blue-light/30 opacity-0 animate-pulse" style="box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);"></view>
      <!-- 内部高光效果 -->
      <view class="absolute top-1 left-1 w-2 h-2 bg-white/40 rounded-full blur-sm"></view>
    </view>
  </transition>
</template>

<script setup lang="ts">
  const showBackTop = ref(false);
  const scrollTop = ref(0);

  const handleScroll = (e: Page.PageScrollOption) => {
    scrollTop.value = e.scrollTop;
    showBackTop.value = scrollTop.value > 300;
  };

  const backToTop = () => {
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 500,
    });
  };

  onPageScroll((e: Page.PageScrollOption) => {
    handleScroll(e);
  });
</script>

<style scoped></style>
