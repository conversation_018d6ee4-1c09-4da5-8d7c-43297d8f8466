
<template>
  <div class="arc-background"></div>
</template>
<style scoped lang="scss">
  .arc-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 540rpx;
    background: linear-gradient(to bottom, #3e8bf7, #3e8bf7 20%, rgba(62, 139, 247, 0.9) 40%, rgba(62, 139, 247, 0.7) 60%, rgba(82, 166, 248, 0.4) 80%, rgba(82, 166, 248, 0) 100%);
    z-index: 0;
  }

  .arc-background::after {
    content: '';
    position: absolute;
    bottom: -60px;
    left: 0;
    width: 100%;
    height: 120px;
    background: radial-gradient(ellipse at center, rgba(106, 178, 255, 0.25) 0%, rgba(126, 188, 255, 0.15) 30%, rgba(146, 198, 255, 0.05) 60%, rgba(166, 208, 255, 0) 80%);
    border-radius: 50% 50% 0 0 / 100% 100% 0 0;
  }
</style>
