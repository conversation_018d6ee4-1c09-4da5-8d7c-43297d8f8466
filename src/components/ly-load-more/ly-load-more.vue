<template>
  <view class="flex items-center justify-center h-10">
    <uni-load-more :status="status" iconType="spinner" />
  </view>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  const props = withDefaults(
    defineProps<{
      status: 'loading' | 'noMore' | 'finished';
      iconType: 'snow' | 'circle' | 'auto';
    }>(),
    {
      status: 'loading',
      iconType: 'snow',
    },
  );
  const emit = defineEmits<{
    (e: 'load'): void;
  }>();
</script>
