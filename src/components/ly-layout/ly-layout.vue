<template>
  <div class="flex flex-col w-full min-h-screen bg-gradient-to-br from-gray-50 to-blue-50" :class="className">
    <ly-arc-bg v-if="isArcBg" />
    <slot />
    <ui-auth />
    <ly-back-up v-if="isBackTop" />
    <van-tabbar v-if="active !== undefined && active !== null" class="z-10" :active="active" active-color="#3e8bf7" @change="handleChange">
      <van-tabbar-item v-for="item in tabBarList" :key="item.pagePath">
        <!-- <image slot="icon" :src="item.icon" style="width: 18px; height: 18px" />
        <image slot="icon-active" :src="item.activeIcon" style="width: 18px; height: 18px" /> -->
        <text slot="icon" :class="item.icon" style="font-size: 24px" />
        <text slot="icon-active" :class="item.activeIcon" style="font-size: 24px" />
        {{ item.text }}
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>
<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      isArcBg?: boolean;
      tabBar?: string;
      active?: number;
      isBackTop?: boolean;
      className?: string;
    }>(),
    {
      isArcBg: false,
      isBackTop: false,
      className: '',
    },
  );

  const tabBarList = [
    {
      icon: 'i-mdi-view-dashboard-outline',
      activeIcon: 'i-mdi-view-dashboard',
      pagePath: '/pages/index/index',
      text: '首页',
    },
    {
      icon: 'i-mdi-store-marker-outline',
      activeIcon: 'i-mdi-store-marker',
      pagePath: '/pages/warehouse/index',
      text: '仓库',
    },
    {
      icon: 'i-mdi-image-outline',
      activeIcon: 'i-mdi-image',
      pagePath: '/pages/image/index',
      text: '社区',
    },
    {
      icon: 'i-mdi-account-settings-outline',
      activeIcon: 'i-mdi-account-settings',
      pagePath: '/pages/user/index',
      text: '我的',
    },
  ];

  const handleChange = (e: any) => {
    uni.switchTab({
      url: tabBarList[e.detail].pagePath,
    });
  };
</script>
<style lang="scss" scoped></style>
