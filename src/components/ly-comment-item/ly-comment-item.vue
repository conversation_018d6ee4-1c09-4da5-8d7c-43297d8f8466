<template>
  <div class="flex space-x-3 py-3" :class="{ 'ml-8': isReply }">
    <!-- 用户头像 -->
    <div class="flex-shrink-0">
      <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 p-0.5">
        <div class="w-full h-full rounded-full bg-white flex items-center justify-center overflow-hidden">
          <image 
            v-if="comment.user.tx" 
            :src="comment.user.tx" 
            class="w-full h-full object-cover rounded-full"
            mode="aspectFill"
          />
          <div v-else class="i-mdi-account text-sm text-gray-400"></div>
        </div>
      </div>
    </div>

    <!-- 评论内容 -->
    <div class="flex-1 min-w-0">
      <!-- 用户名和认证状态 -->
      <div class="flex items-center space-x-2 mb-1">
        <h4 class="text-sm font-medium text-gray-900">{{ comment.user.zsxm || comment.user.nc || '匿名用户' }}</h4>
        <div v-if="comment.user.sfrz === 1" class="w-3 h-3 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
          <div class="i-mdi-check text-white text-xs"></div>
        </div>
        <span class="text-xs text-gray-500">{{ formatTime(comment.createTime) }}</span>
      </div>

      <!-- 回复提示 -->
      <div v-if="comment.replyToUser" class="text-xs text-blue-500 mb-1">
        回复 @{{ comment.replyToUser }}：
      </div>

      <!-- 评论文字 -->
      <p class="text-sm text-gray-800 leading-relaxed mb-2">{{ comment.content }}</p>

      <!-- 操作按钮 -->
      <div class="flex items-center space-x-4">
        <!-- 点赞 -->
        <button 
          @click="toggleLike" 
          class="flex items-center space-x-1 group transition-all duration-300"
          :class="{ 'text-red-500': comment.isLiked, 'text-gray-400': !comment.isLiked }"
        >
          <div class="relative">
            <div 
              :class="[
                'text-sm transition-all duration-300',
                comment.isLiked ? 'i-mdi-heart scale-110' : 'i-mdi-heart-outline group-hover:scale-110'
              ]"
            ></div>
            <!-- 点赞动画 -->
            <div 
              v-show="likeAnimation" 
              class="absolute inset-0 i-mdi-heart text-red-400 animate-ping"
            ></div>
          </div>
          <span v-if="comment.likeCount > 0" class="text-xs">{{ comment.likeCount }}</span>
        </button>

        <!-- 回复 -->
        <button 
          @click="showReply" 
          class="text-xs text-gray-400 hover:text-blue-500 transition-colors duration-300"
        >
          回复
        </button>

        <!-- 更多操作 -->
        <button 
          @click="showMoreActions"
          class="text-xs text-gray-400 hover:text-gray-600 transition-colors duration-300"
        >
          <div class="i-mdi-dots-horizontal"></div>
        </button>
      </div>

      <!-- 回复输入框 -->
      <div v-if="showReplyInput" class="mt-3 transition-all duration-300">
        <div class="flex space-x-2">
          <input 
            v-model="replyText"
            type="text" 
            :placeholder="`回复 @${comment.user.zsxm || comment.user.nc}...`"
            class="flex-1 px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-300"
            @keyup.enter="submitReply"
          />
          <button 
            @click="submitReply"
            :disabled="!replyText.trim()"
            class="px-4 py-2 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300"
          >
            发送
          </button>
          <button 
            @click="cancelReply"
            class="px-3 py-2 text-gray-500 text-xs rounded-lg hover:bg-gray-100 transition-colors duration-300"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Comment } from '@/monkey/types/image';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

interface Props {
  comment: Comment;
  isReply?: boolean;
}

interface Emits {
  (e: 'like', commentId: string): void;
  (e: 'reply', commentId: string, content: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  isReply: false
});

const emit = defineEmits<Emits>();

// 响应式状态
const showReplyInput = ref(false);
const replyText = ref('');
const likeAnimation = ref(false);

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).fromNow();
};

// 点赞操作
const toggleLike = () => {
  // 触发点赞动画
  if (!props.comment.isLiked) {
    likeAnimation.value = true;
    setTimeout(() => {
      likeAnimation.value = false;
    }, 600);
  }
  
  emit('like', props.comment.id);
};

// 显示回复输入框
const showReply = () => {
  showReplyInput.value = true;
  nextTick(() => {
    // 自动聚焦到输入框
    const input = document.querySelector('input[type="text"]') as HTMLInputElement;
    if (input) input.focus();
  });
};

// 提交回复
const submitReply = () => {
  if (!replyText.value.trim()) return;
  
  emit('reply', props.comment.id, replyText.value.trim());
  replyText.value = '';
  showReplyInput.value = false;
};

// 取消回复
const cancelReply = () => {
  replyText.value = '';
  showReplyInput.value = false;
};

// 显示更多操作
const showMoreActions = () => {
  uni.showActionSheet({
    itemList: ['举报', '复制'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          console.log('举报评论');
          break;
        case 1:
          // 复制评论内容
          uni.setClipboardData({
            data: props.comment.content,
            success: () => {
              uni.showToast({
                title: '已复制',
                icon: 'success'
              });
            }
          });
          break;
      }
    }
  });
};
</script>

<style scoped lang="scss">
// 输入框动画
input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

// 按钮悬停效果
button:hover {
  transform: translateY(-1px);
}

// 回复区域进入动画
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style> 