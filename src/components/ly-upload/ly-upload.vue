<template>
  <uni-file-picker  :image-styles="listStyles" v-model="modelValue" :file-mediatype="fileMediatype" :title="title" :limit="limit">
    <slot />
  </uni-file-picker>
</template>
<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      modelValue: string;
      limit: number;
      title: string;
      fileMediatype: string;
      imageStyles: Object;  
    }>(),
    {
      limit: 1,
      title: '',
      fileMediatype: 'image',
      imageStyles: {
        width: 64,
        height: 64,
        border: {
          color: '#ff5a5f',
          width: 2,
          style: 'dashed',
          radius: '2px',
        },
      },
    },
  );

  const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void;
  }>();

  const handleChange = (value: string) => {
    emit('update:modelValue', value);
  };
</script>
