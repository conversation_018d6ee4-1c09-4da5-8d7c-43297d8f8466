<template>
  <view :class="[monkey.$helper.utils.hasSafeArea() ? 'pb-[calc(env(safe-area-inset-bottom))]' : 'pb-3', fixed ? 'fixed' : 'relative']" class="bg-white bottom-0 w-full px-4 pt-3 z-10 shadow-lg border-t border-gray-100">
    <!-- 基于 buttons 数组的按钮组 -->
    <div v-if="buttons.length > 0" :class="getContainerClass()">
      <button
        v-for="(button, index) in buttons"
        :key="button.key || index"
        class="after:border-none"
        :class="[
          getButtonSizeClass(),
          'flex items-center justify-center gap-2 font-medium transition-all duration-200 active:scale-95',
          getButtonTypeClass(button),
          button.loading ? 'opacity-70 cursor-not-allowed' : '',
          button.class || '',
          props.round,
        ]"
        :disabled="button.disabled || button.loading"
        @click="handleButtonClick(button, index)"
      >
        <text v-if="button.loading" class="i-mdi-loading animate-spin text-base"></text>
        <text v-else-if="button.icon" class="text-base" :class="button.icon"></text>
        <text class="text-sm">{{ button.loading ? button.loadingText || '处理中...' : button.text }}</text>
        <text v-if="button.rightIcon && !button.loading" class="text-base" :class="button.rightIcon"></text>
      </button>
    </div>
    <!-- 自定义插槽 -->
    <slot />
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { CommonTypes } from '@/monkey/types';


  // 组件属性定义
  const props = withDefaults(
    defineProps<{
      buttons?: CommonTypes.ActionItem[];
      fixed?: boolean;
      layout?: 'flex' | 'grid';
      gap?: 'small' | 'medium' | 'large';
      size?: 'small' | 'medium' | 'large';
      round?: 'rounded-lg' | 'rounded-full';
    }>(),
    {
      buttons: () => [],
      fixed: true,
      layout: 'flex',
      gap: 'medium',
      size: 'medium',
      round: 'rounded-lg',
    },
  );

  // 事件定义
  const emit = defineEmits<{
    'button-click': [button: CommonTypes.ActionItem, index: number];
  }>();

  // 获取容器样式类
  const getContainerClass = () => {
    const gapMap = {
      small: 'gap-2',
      medium: 'gap-3',
      large: 'gap-4',
    };

    if (props.layout === 'grid') {
      return `grid grid-cols-${props.buttons.length} ${gapMap[props.gap]}`;
    }

    return `flex ${gapMap[props.gap]}`;
  };

  // 获取按钮尺寸样式类
  const getButtonSizeClass = () => {
    const sizeMap = {
      small: 'h-10 px-3 text-xs',
      medium: 'h-12 px-4 text-sm',
      large: 'h-14 px-6 text-base',
    };

    // 单个按钮时占满宽度，多个按钮时平分
    const widthClass = props.buttons.length === 1 ? 'w-full' : 'flex-1';

    return `${widthClass} ${sizeMap[props.size]}`;
  };

  // 获取按钮类型样式类
  const getButtonTypeClass = (button: CommonTypes.ActionItem) => {
    // 如果按钮有自定义 class，优先使用
    if (button.class) {
      return '';
    }

    const type = button.type || 'primary';
    const typeMap = {
      primary: 'bg-theme-blue text-white active:bg-blue-600',
      secondary: 'bg-gray-100 text-gray-700 active:bg-gray-200 border border-gray-300',
      danger: 'bg-red-500 text-white active:bg-red-600',
      success: 'bg-green-500 text-white active:bg-green-600',
      warning: 'bg-yellow-500 text-white active:bg-yellow-600',
    };

    return typeMap[type] || typeMap.primary;
  };

  // 按钮点击处理
  const handleButtonClick = (button: CommonTypes.ActionItem, index: number) => {
    if (button.disabled || button.loading) return;

    // 优先执行按钮自身的点击事件
    if (button.click) {
      button.click();
    }

    // 然后触发组件事件
    emit('button-click', button, index);
  };
</script>

<style lang="scss" scoped>

</style>
