<template>
  <div class="flex items-center font-bold" :class="color">
    <text class="i-mdi-currency-cny text-[10px]">¥</text>
    <text :class="[size]">{{ price }}</text>
  </div>
</template>

<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      price: number;
      color?: string;
      size?: string;
    }>(),
    {
      price: 0,
      color: 'text-red-500',
      size: 'text-xs',
    },
  );
</script>
