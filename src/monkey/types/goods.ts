// ===========================================
// 商品相关类型定义
// ===========================================

/**
 * 商品信息
 */
export interface Goods {
  /** 商品ID */
  id?: string;
  /** 商品名称 */
  name: string;
  /** 原价 */
  originalPrice: string;
  /** 现价 */
  price: string;
  /** 单位 */
  unit: string;
  /** 销量 */
  sales: number;
  /** 产地 */
  location: string;
  /** 商品图片 */
  image: string;
  /** 折扣信息 */
  discount: string;
  /** 标签列表 */
  tags: string[];
  /** 店铺名称 */
  shop: string;
  /** 商品描述 */
  description?: string;
  /** 库存数量 */
  stock?: number;
  /** 商品状态 */
  status?: 'active' | 'inactive' | 'soldout';
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/**
 * 商品搜索参数
 */
export interface GoodsSearchParams {
  /** 搜索关键词 */
  keyword?: string;
  /** 分类ID */
  categoryId?: string;
  /** 价格范围 - 最低价 */
  minPrice?: number;
  /** 价格范围 - 最高价 */
  maxPrice?: number;
  /** 排序方式 */
  sortBy?: 'price' | 'sales' | 'createTime';
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 页码 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
}

/**
 * 商品分类
 */
export interface GoodsCategory {
  /** 分类ID */
  id: string;
  /** 分类名称 */
  name: string;
  /** 父级分类ID */
  parentId?: string;
  /** 分类图标 */
  icon?: string;
  /** 排序 */
  sort?: number;
  /** 子分类 */
  children?: GoodsCategory[];
}
