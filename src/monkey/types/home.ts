export interface BannerData {
  id: string;
  tpzt: string;
  tpzt_key: string;
  sftz: string;
  sftz_key: string;
  tpmc: string;
  zsnr: string;
  px: string;
  tp: Array<{
    refId: string;
  }>;
  creater: Array<{
    id: string;
    name: string;
  }>;
}

export interface BannerItem {
  id: string;
  url?: string;
  name: string;
  isLink?: string;
  zsnr?: string;
  createTime?: string;
}

export interface InboundDateItem {
  id: string;
  week: string;
  date: string;
  dateStr?: string;
  isToday?: boolean;
  isExpired?: boolean;
  isDisabled?: boolean;
}

export interface InboundTimeItem {
  id: string;
  time: string;
  count: number;
  date?: string;
  isDisabled?: boolean;
  isExpired?: boolean;
}