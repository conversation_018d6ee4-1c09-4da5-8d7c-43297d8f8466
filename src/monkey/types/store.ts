// ===========================================
// Stores 类型定义
// ===========================================

import type { UserProfile } from '../types';

/**
 * 用户状态接口
 */
export interface UserState {
  user: UserProfile | null;
  token: string | null;
  hasLogin: boolean;
  sessionKey: string | null;
  authineToken: string | null;
  authineTokenExpiresIn: number | null;
}

/**
 * 模态框状态接口
 */
export interface ModalState {
  visible: boolean;
}
