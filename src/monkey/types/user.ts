// ===========================================
// 用户相关类型定义
// ===========================================
import { UploadResult } from './common';

/**
 * OCR 身份证识别结果
 */
export interface OcrCardRecognition {
  /** 身份证号 */
  card: string;
  /** 真实姓名 */
  name: string;
  /** 地址 */
  address?: string;
  /** 出生日期 */
  birthday?: string;
  /** 性别 */
  sex?: string;
  /** 民族 */
  fork?: string;
}

/**
 * 身份证识别结果
 */
export interface OcrCardRecognitionResult {
  /** 错误信息 */
  subMsg?: string;
  /** 请求序列号 */
  sysReqSn?: string;
  /** 错误码 */
  code?: string;
  /** 交易号 */
  jyTradeNo?: string;
  /** 错误信息 */
  msg?: string;
  /** 时间戳 */
  timestamp?: string;
  /** 签名 */
  sign?: string;
  /** 子错误码 */
  subCode?: string;
}

/**
 * 用户认证表单数据
 */
export interface UserAuthFormData {
  /** 身份证正面信息 */
  idCardFront: {
    /** 手机号 */
    phone: string;
    /** 图片路径 */
    tempFilePath: string;
    /** 身份证号 */
    card: string;
    /** 真实姓名 */
    name: string;
    /** 地址 */
    address?: string;
    /** 出生日期 */
    birthday?: string;
    /** 性别 */
    sex?: string;
    /** 民族 */
    fork?: string;
  };
  /** 身份证背面信息 */
  idCardBack: {
    /** 图片路径 */
    tempFilePath: string;
  };
  /** 身份证正面图片 */
  frontImage: UploadResult;
  /** 身份证背面图片 */
  backImage: UploadResult;
}

/**
 * 用户认证状态
 */
export interface UserAuthStatus {
  /** 认证状态 */
  status: 'pending' | 'success' | 'failed' | 'reviewing';
  /** 认证消息 */
  message?: string;
  /** 认证时间 */
  authTime?: string;
}

/**
 * 用户基本信息
 */
export interface UserProfile {
  /** 用户ID */
  id: string;
  /** 昵称 */
  nc?: string;
  /** 头像 */
  tx?: string;
  /** 手机号 */
  sjh?: string;
  /** 邮箱 */
  email?: string;
  /** 真实姓名 */
  zsxm?: string;
  /** 身份证号 */
  sfzhm?: string;
  /** 性别 1-男 2-女 */
  sex?: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 是否认证 */
  sfrz: number;
  /** 微信openId */
  OpenId?: string;
  /** 身份证背面图片 */
  sfzbm?: Array<UploadResult>;
  /** 身份证正面图片 */
  sfzfm?: Array<UploadResult>;
}

/**
 * 地址信息
 */
export interface AddressInfo {
  /** 省份代码 */
  provinceCode: string;
  /** 省份名称 */
  provinceName: string;
  /** 城市代码 */
  cityCode: string;
  /** 城市名称 */
  cityName: string;
  /** 区县代码 */
  areaCode: string;
  /** 区县名称 */
  areaName: string;
  /** 详细地址 */
  detail: string;
}
