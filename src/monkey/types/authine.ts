/**
 * 运行时查询列表参数
 */
export interface AuthineListParams {
  filters: any[];
  mobile: boolean;
  page: number;
  queryCode: string;
  schemaCode: string;
  size: number;
  queryVersion: number;
  queryCondition: any[];
  showTotal: boolean;
  customDisplayColumns: any[];
  customQueryFields: any[];
}

/**
 * 低代码表单查询详情参数
 */
export interface AuthineFormParams {
  sheetCode: string;
  objectId: string;
  schemaCode: string;
  isWorkFlow: boolean;
  _viewCode: string;
  return: number;
  pageInfo: string;
  relevanceInfo: string;
  isMobile: boolean;
}

/**
 * 低代码列表查询条件
 */
export interface AuthineListQueryCondition {
  queryFilterType: string;
  propertyCode: string;
  propertyType: number;
  propertyValue: string;
}

export interface AuthineListResponse<T> {
  content: T[];
}

export interface AuthineFormResponse<T> {
  bizObject: {
    data: T;
  };
}