// ===========================================
// 通用类型定义
// ===========================================

// 按钮接口定义
export interface ActionItem {
  key?: string | number;
  text: string;
  icon?: string;
  rightIcon?: string;
  type?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning';
  size?: 'small' | 'medium' | 'large';
  class?: string;
  disabled?: boolean;
  loading?: boolean;
  loadingText?: string;
  click?: () => void;
}

/**
 * 模态框配置选项
 */
export interface ModalOptions {
  /** 标题 */
  title: string;
  /** 内容 */
  content: string;
  /** 确认按钮文字 */
  confirmText: string;
  /** 取消按钮文字 */
  cancelText: string;
  /** 确认按钮颜色 */
  confirmColor: string;
  /** 取消按钮颜色 */
  cancelColor: string;
  /** 成功回调 */
  success: (res: any) => void;
  /** 失败回调 */
  fail: (err: any) => void;
}

/**
 * 文件上传结果
 */
export interface UploadResult {
  /** 文件ID */
  id: string;
  /** 文件名 */
  name: string;
  /** 文件大小 */
  fileSize: number;
  /** 文件类型 */
  mimeType: string;
  /** 文件扩展名 */
  fileExtension: string;
  /** 文件URL */
  url: string;
  /** 文件创建时间 */
  createdTime: string;
  /** 文件修改时间 */
  modifiedTime: string;
  /** 文件创建者 */
  createdBy: string;
  /** 文件修改者 */
  modifiedBy: string;
  /** 文件备注 */
  remarks: string;
  /** 文件模式 */
  schemaCode: string;
  /** 文件引用ID */
  refId: string;
  /** 文件业务对象ID */
  bizObjectId: string;
  /** 文件业务属性代码 */
  bizPropertyCode: string;
  /** 是否删除 */
  deleted: boolean;
}

/**
 * 请求选项
 */
export interface RequestOptions {
  url: string;
  method: string;
  data?: any;
  headers?: any;
  timeout?: number;
  source?: string;
  loading?: {
    show: boolean;
    title: string;
  };
  token?: boolean;
}

// ===========================================
// 常用工具类型
// ===========================================

// API 响应基础结构
export interface ApiResponse<T = any> {
  code?: number;
  errcode?: number;
  msg?: string;
  data?: T;
  success?: boolean;
  errmsg?: string;
}

// 分页数据结构
export interface PaginationData<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 分页响应结构
export type PaginationResponse<T = any> = ApiResponse<PaginationData<T>>;

// 表单验证规则
export interface FormRule {
  required?: boolean;
  pattern?: RegExp;
  minLength?: number;
  maxLength?: number;
  errorMessage: string;
}

// 表单验证规则集合
export type FormRules = Record<string, { rules: FormRule[] }>;

// ===========================================
// 类型工具函数
// ===========================================

// 获取对象所有键的联合类型
export type KeysOf<T> = keyof T;

// 获取对象所有值的联合类型
export type ValuesOf<T> = T[keyof T];

// 可选属性类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// 必需属性类型
export type Required<T, K extends keyof T> = T & Required<Pick<T, K>>;
