:root {
  --tabbar-item-margin-bottom: 2px;
  --index-bar-index-font-size: 12px;
  --primary-blue: #3e8bf7;
  --primary-blue-light: #52a6f8;
  --primary-blue-dark: #2563eb;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

body {
	width: 100vw;
	height: 100vh;
}

.cu-btn-style {
	@apply w-[690rpx] flex items-center justify-center rounded-full !text-white text-sm !py-3 bg-gradient-to-br from-theme-blue to-theme-blue tracking-widest shadow after:!border-none active:bg-gradient-to-br active:from-theme-blue-600 active:to-theme-blue-600  font-medium transition-colors;
}

.cu-cancel-btn {
	@apply w-[690rpx] flex items-center justify-center rounded-full !text-white text-sm !py-3 bg-gradient-to-br from-red-500 to-red-500 tracking-widest shadow after:!border-none active:bg-gradient-to-br active:from-red-600 active:to-red-600  font-medium transition-colors;
}

.bg-gradient-to-b-theme-blue {
	@apply bg-gradient-to-b from-[#3e8bf7] via-[#3e8bf7]/100 to-[#52a6f8]/100;
}