import * as utils from './utils';
import * as toast from './toast';
import * as param from './param';
import type { ModalOptions } from '../types';

/**
 * 工具函数接口定义
 */
export interface UtilsInterface {
  /** 判断是否支持安全区 */
  hasSafeArea: () => boolean;
  /** 获取导航栏背景颜色 */
  getNavBarBgColor: (scrollTop: number) => string;
  /** 获取导航栏标题颜色 */
  getNavTitleColor: (scrollTop: number) => string;
  /** 手机号码脱敏处理 */
  hidePhone: (phone: string) => string;
  /** 身份证号脱敏处理 */
  hideIdCard: (idCard: string) => string;
  /** 获取导航栏高度 */
  getNavBarHeight: () => number;
  /** 获取认证状态文本 */
  getAuthStatusText: (status: number) => string;
  /** 获取认证状态样式类 */
  getAuthStatusClass: (status: number) => string;
  /** 预览图片 */
  previewImage: (url: string) => void;
  /** 格式化文件大小 */
  formatFileSize: (bytes: number) => string;
  /** 防抖函数 */
  debounce: <T extends (...args: any[]) => any>(func: T, wait: number) => (...args: Parameters<T>) => void;
  /** 节流函数 */
  throttle: <T extends (...args: any[]) => any>(func: T, limit: number) => (...args: Parameters<T>) => void;
  /** 生成随机32位字符串 */
  generateRandomString: (length?: number) => string;
}

/**
 * Toast 提示接口定义
 */
export interface ToastInterface {
  /** 成功提示 */
  success: (title: string, duration?: number) => void;
  /** 错误提示 */
  error: (title: string, duration?: number) => void;
  /** 警告提示 */
  warning: (title: string, duration?: number) => void;
  /** 加载提示 */
  loading: (title?: string) => void;
  /** 隐藏加载提示 */
  hideLoading: () => void;
  /** 模态框 */
  modal: (options: ModalOptions) => Promise<boolean>;
  /** 确认对话框 */
  confirm: (content: string, title?: string) => Promise<boolean>;
  /** 警告对话框 */
  alert: (content: string, title?: string) => Promise<boolean>;
}

/**
 * 参数处理接口定义
 */
export interface ParamInterface {
  /** 获取低代码列表参数 */
  getAuthineListParams: (options: AuthineTypes.AuthineListParams) => AuthineTypes.AuthineListParams;
  /** 获取低代码列表查询条件 */
  getAuthineListQueryCondition: (options: AuthineTypes.AuthineListQueryCondition) => AuthineTypes.AuthineListQueryCondition;
  /** 获取低代码表单查询参数 */
}

/**
 * Helper 主接口定义
 */
export interface HelperInterface {
  /** 工具函数集合 */
  utils: UtilsInterface;
  /** Toast 提示集合 */
  toast: ToastInterface;
  /** 参数处理集合 */
  param: ParamInterface;
}

/**
 * 统一的 Helper 工具集合
 * 提供常用的工具函数和 UI 交互方法
 */
const helper: HelperInterface = {
  utils,
  toast,
  param,
} as const;

export default helper;
