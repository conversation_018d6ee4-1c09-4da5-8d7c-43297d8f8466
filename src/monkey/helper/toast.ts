import type { ModalOptions } from '../types';

/**
 * Toast 默认配置
 */
const DEFAULT_DURATION = 2000;
const MAX_TITLE_LENGTH = 20;

/**
 * Toast 类型枚举
 */
export enum ToastType {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'none',
  LOADING = 'loading',
}

/**
 * 显示 Toast 的通用方法
 * @param title 提示文本
 * @param icon 图标类型
 * @param duration 显示时长
 */
const showToast = (title: string, icon: string, duration: number = DEFAULT_DURATION): void => {
  // 限制标题长度
  const displayTitle = title.length > MAX_TITLE_LENGTH
    ? `${title.substring(0, MAX_TITLE_LENGTH)}...`
    : title;

  uni.showToast({
    title: displayTitle,
    icon: icon as any,
    duration,
    mask: false, // 防止用户误触
  });
};

/**
 * 成功提示
 * @param title 提示文本
 * @param duration 显示时长（毫秒），默认 2000ms
 */
export const success = (title: string, duration: number = DEFAULT_DURATION): void => {
  showToast(title, ToastType.SUCCESS, duration);
};

/**
 * 错误提示
 * @param title 提示文本
 * @param duration 显示时长（毫秒），默认 2000ms
 */
export const error = (title: string, duration: number = DEFAULT_DURATION): void => {
  showToast(title, ToastType.ERROR, duration);
};

/**
 * 警告提示
 * @param title 提示文本
 * @param duration 显示时长（毫秒），默认 2000ms
 */
export const warning = (title: string, duration: number = DEFAULT_DURATION): void => {
  showToast(title, ToastType.WARNING, duration);
};

/**
 * 显示加载提示
 * @param title 加载文本，默认为 "加载中..."
 */
export const loading = (title: string = '加载中...'): void => {
  // 限制标题长度
  const displayTitle = title.length > MAX_TITLE_LENGTH
    ? `${title.substring(0, MAX_TITLE_LENGTH)}...`
    : title;

  uni.showLoading({
    title: displayTitle,
    mask: true, // 加载时阻止用户操作
  });
};

/**
 * 隐藏加载提示
 */
export const hideLoading = (): void => {
  try {
    uni.hideLoading();
  } catch (error) {
    console.warn('隐藏加载提示失败:', error);
  }
};

/**
 * 模态框默认配置
 */
const DEFAULT_MODAL_CONFIG = {
  confirmText: '确定',
  cancelText: '取消',
  confirmColor: '#3E8BF7',
  cancelColor: '#333333',
  showCancel: true,
  showConfirm: true,
} as const;

/**
 * 显示模态框
 * @param options 模态框配置选项
 * @returns Promise<boolean> 用户是否点击确定
 */
export const modal = (options: ModalOptions): Promise<boolean> => {
  const config = { ...DEFAULT_MODAL_CONFIG, ...options };

  return new Promise((resolve, reject) => {
    try {
      uni.showModal({
        title: config.title,
        content: config.content,
        confirmText: config.confirmText,
        cancelText: config.cancelText,
        confirmColor: config.confirmColor,
        cancelColor: config.cancelColor,
        showCancel: config.showCancel,
        showConfirm: config.showConfirm,
        success: (result: { confirm: boolean; cancel: boolean }) => {
          resolve(result.confirm);
        },
        fail: (error: any) => {
          console.error('模态框显示失败:', error);
          reject(error);
        },
      });
    } catch (error) {
      console.error('模态框调用失败:', error);
      reject(error);
    }
  });
};

/**
 * 显示确认对话框（简化版）
 * @param content 对话框内容
 * @param title 对话框标题，默认为 "提示"
 * @returns Promise<boolean> 用户是否点击确定
 */
export const confirm = (content: string, title: string = '提示'): Promise<boolean> => {
  return modal({
    title,
    content,
    showCancel: true,
    showConfirm: true,
  });
};

/**
 * 显示警告对话框（只有确定按钮）
 * @param content 对话框内容
 * @param title 对话框标题，默认为 "警告"
 * @returns Promise<boolean> 始终返回 true
 */
export const alert = (content: string, title: string = '警告'): Promise<boolean> => {
  return modal({
    title,
    content,
    showCancel: false,
    showConfirm: true,
  });
};
