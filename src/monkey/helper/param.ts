import { AuthineTypes } from '../types';

/**
 * 获取低代码列表参数
 * @param options 查询参数
 * @returns 低代码列表参数
 */
export const getAuthineListParams = (options: AuthineTypes.AuthineListParams) => {
  const { schemaCode, queryCondition = [], showTotal = true, page = 0, size = 20 } = options;
  return {
    filters: [],
    mobile: false,
    page,
    queryCode: schemaCode,
    schemaCode,
    size,
    queryVersion: 1,
    queryCondition: [[queryCondition]],
    showTotal,
    customDisplayColumns: [],
    customQueryFields: [],
  };
};

export const getAuthineFormParams = (options: AuthineTypes.AuthineFormParams) => {
  const { objectId, schemaCode, isWorkFlow = false, pageInfo = '', relevanceInfo = {} } = options;
  return {
    sheetCode: schemaCode,
    objectId,
    schemaCode: schemaCode,
    isWorkFlow,
    _viewCode: schemaCode,
    return: Date.now(),
    pageInfo,
    relevanceInfo,
    isMobile: false,
  };
};

/**
 * 获取低代码列表查询条件
 * @param queryFilterType 查询条件类型
 * @param propertyCode 属性代码
 * @param propertyType 属性类型
 * @param propertyValue 属性值
 * @returns 低代码列表查询条件
 */
export const getAuthineListQueryCondition = (queryFilterType: string, propertyCode: string, propertyType: number, propertyValue: string): AuthineTypes.AuthineListQueryCondition => {
  return {
    queryFilterType,
    propertyCode,
    propertyType,
    propertyValue,
  };
};
