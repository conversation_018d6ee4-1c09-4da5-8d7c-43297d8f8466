import monkey from '@/monkey';
import { AuthineTypes } from '../types';

/**
 * 获取低代码列表参数
 * @param options 查询参数
 * @returns 低代码列表参数
 */
export const getAuthineListParams = (options: AuthineTypes.AuthineListParams) => {
  const { schemaCode, queryCondition = [], showTotal = true, page = 0, size = 20 } = options;
  return {
    filters: [],
    mobile: false,
    page,
    queryCode: schemaCode,
    schemaCode,
    size,
    queryVersion: 1,
    queryCondition: [[queryCondition]],
    showTotal,
    customDisplayColumns: [],
    customQueryFields: [],
  };
};

export const getAuthineFormParams = (options: AuthineTypes.AuthineFormParams) => {
  const { objectId, schemaCode, isWorkFlow = false, pageInfo = '', relevanceInfo = {} } = options;
  return {
    sheetCode: schemaCode,
    objectId,
    schemaCode: schemaCode,
    isWorkFlow,
    _viewCode: schemaCode,
    return: Date.now(),
    pageInfo,
    relevanceInfo,
    isMobile: false,
  };
};

/**
 * 获取低代码列表查询条件
 * @param queryFilterType 查询条件类型
 * @param propertyCode 属性代码
 * @param propertyType 属性类型
 * @param propertyValue 属性值
 * @returns 低代码列表查询条件
 */
export const getAuthineListQueryCondition = (queryFilterType: string, propertyCode: string, propertyType: number, propertyValue: string): AuthineTypes.AuthineListQueryCondition => {
  return {
    queryFilterType,
    propertyCode,
    propertyType,
    propertyValue,
  };
};

export const getAuthineFormSubmitApproval = (options: AuthineTypes.AuthineFormSubmitApproval) => {
  const { activityCode, commonSet = false, deleted = false, result = 1 } = options;
  return {
    workItemId: null,
    workflowInstanceId: '',
    workflowTokenId: null,
    activityCode,
    activityName: null,
    commonSet,
    deleted,
    result,
  };
};

export const getAuthineFormSubmitBizObject = (options: AuthineTypes.AuthineFormSubmitBizObject) => {
  const { id = monkey.$helper.utils.generateRandomString(), data = {}, schemaCode = '', workflowInstanceId = null } = options;
  return {
    id,
    data,
    schemaCode: schemaCode,
    sheetCode: schemaCode,
    workflowInstanceId,
  };
};

/**
 * 获取低代码表单提交参数
 * @param options 表单提交参数
 * @param bizObject 表单业务对象
 * @param approval 表单审批对象
 * @returns 表单提交参数
 */
export const getAuthineFormSubmitParams = async (options: AuthineTypes.AuthineFormSubmitParams, bizObject: AuthineTypes.AuthineFormSubmitBizObject, approval: AuthineTypes.AuthineFormSubmitApproval) => {
  const { workflowCode, actionCode = 'submit', formType = '1' } = options;
  try {
    const replayToken = await monkey.$api.authine.getAuthineFormReplayToken();
    if (replayToken.errcode !== 0) {
      monkey.$helper.toast.error(replayToken.errmsg);
      return null;
    }
    return {
      formType,
      actionCode,
      bizObject,
      agree: true,
      approval: approval,
      latestSign: null,
      replayToken: replayToken.data,
      workflowCode,
      workflowInstanceId: null,
      workItemId: null,
      queryId: '',
    } as AuthineTypes.AuthineFormSubmitParams;
  } catch (error) {
    console.error('获取提交参数失败:', error);
    monkey.$helper.toast.error('获取提交参数失败');
    return null;
  }
};


export const getAuthineFormDeleteParams = (options: AuthineTypes.AuthineFormDeleteParams) => {
  const { ids, schemaCode } = options;
  return {
    ids,
    schemaCode,
  };
};