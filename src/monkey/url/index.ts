import monkey from '@/monkey';

/**
 * 图片缩略图参数接口
 */
interface ThumbnailParams {
  /** 图片宽度，默认200 */
  width?: string | number;
  /** 图片高度，默认200 */
  height?: string | number;
  /** 缩放模式：lfit(等比缩放), mfit(强制缩放), fill(填充), pad(填充背景), fixed(固定尺寸) */
  mode?: 'lfit' | 'mfit' | 'fill' | 'pad' | 'fixed';
  /** 压缩质量，1-100，默认90 */
  quality?: number;
  /** 裁剪位置，默认center */
  gravity?: 'north_west' | 'north' | 'north_east' | 'west' | 'center' | 'east' | 'south_west' | 'south' | 'south_east';
}

/**
 * 对象存储文件系统类型
 */
type FileSystemType = 'public' | 'aliyun' | 'qcloud' | 'qiniu';

/**
 * 裁剪位置格式化类型
 */
type GravityFormatType = 'aliyun' | 'qcloud' | 'qiniu';

/**
 * 添加CDN域名前缀
 * @param url - 图片URL地址
 * @param cdnurl - CDN域名，为空时使用默认配置
 * @returns 完整的图片URL地址
 */
const cdn = (url: string = '', cdnurl: string = ''): string => {
  if (!url) return '';
  if (url.indexOf('http') === 0) {
    return url;
  }
  if (cdnurl === '') {
    cdnurl = monkey.$config.target.VIEW_IMAGE_URL;
  }
  return cdnurl + url;
};
/**
 * URL处理工具集合
 */
export default {
  /**
   * 添加CDN域名前缀
   * @param url - 图片URL地址
   * @param cdnurl - CDN域名，为空时使用默认配置
   * @returns 完整的图片URL地址
   */
  cdn,

  /**
   * 对象存储自动剪裁缩略图
   * @param url - 原始图片URL
   * @param params - 缩略图参数配置
   * @returns 带缩略图参数的完整URL
   */
  thumb: (url: string = '', params: ThumbnailParams): string => {
    url = cdn(url);
    return append_thumbnail_params(url, params);
  },

  /**
   * 静态资源地址处理
   * @param url - 静态资源URL
   * @param staticurl - 静态资源域名，为空时使用默认配置
   * @returns 处理后的静态资源URL
   */
  static: (url: string = '', staticurl: string = ''): string => {
    if (staticurl === '') {
      staticurl = monkey.$config.target.VIEW_IMAGE_URL;
    }
    if (staticurl !== 'local') {
      url = cdn(url, staticurl);
    }
    return url;
  },

  /**
   * CSS背景图片地址处理
   * @param url - 图片URL
   * @param staticurl - 静态资源域名，为空时使用默认配置
   * @returns CSS url()格式的背景图片地址
   */
  css: (url: string = '', staticurl: string = ''): string => {
    if (staticurl === '') {
      staticurl = monkey.$config.target.VIEW_IMAGE_URL;
    }
    if (staticurl !== 'local') {
      url = cdn(url, staticurl);
    }
    return `url(${url})`;
  },
};

/**
 * 追加对象存储自动裁剪/压缩参数
 * @param url - 原始图片URL
 * @param params - 缩略图参数配置
 * @returns 带参数的完整URL
 */
function append_thumbnail_params(url: string, params: ThumbnailParams): string {
  const filesystem: FileSystemType = 'aliyun';
  if (filesystem === 'public') {
    return url;
  }

  // 参数默认值设置
  let width = String(params.width || '200'); // 宽度
  let height = String(params.height || '200'); // 高度
  let mode = params.mode || 'lfit'; // 缩放模式
  let quality = params.quality || 90; // 压缩质量
  let gravity = params.gravity || 'center'; // 裁剪位置

  // 初始化变量
  let suffix = '';
  let crop_str = '';
  let quality_str = '';
  let size = width + 'x' + height;
  // 根据不同的文件系统生成对应的参数
  switch (filesystem) {
    case 'aliyun':
      // 阿里云OSS图片处理
      // 裁剪处理
      if (gravity && gravity !== 'center') {
        // 指定了裁剪区域
        mode = 'mfit';
        crop_str = '/crop,g_' + gravityFormatMap('aliyun', gravity) + ',w_' + width + ',h_' + height;
      }

      // 质量压缩处理
      if (quality > 0 && quality < 100) {
        quality_str = '/quality,q_' + quality;
      }

      // 缩放参数
      suffix = 'x-oss-process=image/resize,m_' + mode + ',w_' + width + ',h_' + height;

      // 拼接裁剪和质量压缩参数
      suffix += crop_str + quality_str;
      break;
    case 'qcloud':
      // 腾讯云COS图片处理
      let mode_str = 'thumbnail';
      if (mode === 'fill' || (gravity && gravity !== 'center')) {
        // 指定了裁剪区域
        mode_str = 'crop';
        mode = 'fill';
        crop_str = '/gravity/' + gravityFormatMap('qcloud', gravity);
      }

      // 质量压缩处理
      if (quality > 0 && quality < 100) {
        quality_str = '/rquality/' + quality;
      }

      // 根据缩放模式调整尺寸参数
      switch (mode) {
        case 'lfit':
          size = '' + size + '>';
          break;
        case 'mfit':
          size = '!' + size + 'r';
          break;
        case 'fill':
          break;
        case 'pad':
          size = size + '/pad/1';
          break;
        case 'fixed':
          size = size + '!';
          break;
      }

      suffix = 'imageMogr2/' + mode_str + '/' + size + crop_str + quality_str;
      break;
    case 'qiniu':
      // 七牛云图片处理
      if (mode === 'fill' || (gravity && gravity !== 'center')) {
        // 指定了裁剪区域,全部转为 mfit
        mode = 'mfit';
        crop_str = '/gravity/' + gravityFormatMap('qiniu', gravity) + '/crop/' + size;
      }

      // 质量压缩处理
      if (quality > 0 && quality < 100) {
        quality_str = '/quality/' + quality;
      }

      // 根据缩放模式调整尺寸参数
      switch (mode) {
        case 'lfit':
        case 'pad': // 七牛不支持在缩放之后，尺寸不足时，填充背景色,所以这里和 lfit 模式一样
          size = size + '>';
          break;
        case 'mfit':
          size = '!' + size + 'r';
          break;
        case 'fill':
          // 会被转为 mfit
          break;
        case 'fixed':
          size = size + '!';
          break;
      }

      suffix = 'imageMogr2/thumbnail/' + size + crop_str + quality_str;
      break;
  }

  return url + '?' + suffix;
}

/**
 * 裁剪区域格式转换
 * 将统一的裁剪区域字符转换为不同云服务商的格式
 * @param type - 云服务商类型：aliyun|qcloud|qiniu
 * @param gravity - 统一的裁剪区域字符
 * @returns 转换后的裁剪区域字符
 */
function gravityFormatMap(type: GravityFormatType, gravity: string): string {
  // 不同云服务商的裁剪区域格式映射表
  const gravityFormat: Record<GravityFormatType, Record<string, string>> = {
    // 阿里云OSS裁剪区域格式
    aliyun: {
      north_west: 'nw', // 左上
      north: 'north', // 中上
      north_east: 'ne', // 右上
      west: 'west', // 左中
      center: 'center', // 中部
      east: 'east', // 右中
      south_west: 'sw', // 左下
      south: 'south', // 中下
      south_east: 'se', // 右下
    },
    // 腾讯云COS裁剪区域格式
    qcloud: {
      northwest: 'nw', // 左上
      north: 'north', // 中上
      northeast: 'ne', // 右上
      west: 'west', // 左中
      center: 'center', // 中部
      east: 'east', // 右中
      southwest: 'sw', // 左下
      south: 'south', // 中下
      southeast: 'se', // 右下
    },
    // 七牛云裁剪区域格式
    qiniu: {
      NorthWest: 'nw', // 左上
      North: 'north', // 中上
      NorthEast: 'ne', // 右上
      West: 'west', // 左中
      Center: 'center', // 中部
      East: 'east', // 右中
      SouthWest: 'sw', // 左下
      South: 'south', // 中下
      SouthEast: 'se', // 右下
    },
  };

  return gravityFormat[type]?.[gravity] || gravity;
}
