// ===========================================
// API 统一导出入口
// ===========================================

// 导入各模块的 API 方法
import * as medicineApi from './medicine';
import * as userApi from './user';
import * as authApi from './auth';
import * as authineApi from './authine';
import * as reservationApi from './reservation';
// 导入类型定义
import type { MedicineTypes, UserTypes, AuthineTypes, CommonTypes, HomeTypes } from '../types';

// ===========================================
// API 方法类型定义
// ===========================================

export interface MedicineApi {
  getMedicinalItemList: () => Promise<CommonTypes.ApiResponse<MedicineTypes.MedicinalItem[]>>;
  getMedicinalItemDetail: (ycbm: string) => Promise<CommonTypes.ApiResponse<MedicineTypes.MedicinalItem>>;
}

export interface UserApi {
  getWxLoginCode: (code: string) => Promise<CommonTypes.ApiResponse<UserTypes.UserProfile>>;
  getWxPhone: (code: string) => Promise<CommonTypes.ApiResponse<string>>;
  getCurrentUserInfo: (openId: string) => Promise<CommonTypes.ApiResponse<UserTypes.UserProfile>>;
  getCode: () => Promise<CommonTypes.ApiResponse<any>>;
}

export interface AuthApi {
  getOcrCardRecognition: (image: string) => Promise<CommonTypes.ApiResponse<any>>;
  uploadImage: (image: string) => Promise<CommonTypes.ApiResponse<string>>;
  getAuthToken: (code: string) => Promise<CommonTypes.ApiResponse<string>>;
}

export interface AuthineApi {
  getAuthineList: (params: AuthineTypes.AuthineListParams) => Promise<CommonTypes.ApiResponse<AuthineTypes.AuthineListResponse<HomeTypes.BannerData>>>;
  getAuthineForm: (params: AuthineTypes.AuthineFormParams) => Promise<CommonTypes.ApiResponse<AuthineTypes.AuthineFormResponse<HomeTypes.BannerData>>>;
  submitAuthineForm: (params: AuthineTypes.AuthineFormSubmitParams) => Promise<CommonTypes.ApiResponse<any>>;
  getEnableRecordsByDictionaryId: (dicId: string) => Promise<CommonTypes.ApiResponse<any>>;
}

export interface ReservationApi {
  getReservationDateList: (data: any) => Promise<CommonTypes.ApiResponse<AuthineTypes.AuthineListResponse<any>>>;
}

export interface Api {
  medicine: MedicineApi;
  user: UserApi;
  auth: AuthApi;
  authine: AuthineApi;
  reservation: ReservationApi;
}

// ===========================================
// API 实例
// ===========================================

const api: Api = {
  medicine: medicineApi,
  user: userApi,
  auth: authApi,
  authine: authineApi,
};

// ===========================================
// 导出方式
// ===========================================

// 默认导出 - 保持向后兼容
export default api;
