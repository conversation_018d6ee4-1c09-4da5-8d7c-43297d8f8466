import { getWxPhone } from './user';
import request from '../request';

/**
 * 获取用户信息 微信登录
 * @param code 微信小程序code
 * @returns
 */
export const getWxLoginCode = (code: string) => {
  return request({
    url: `/api/wx/login/getUserInfo?code=${code}`,
    method: 'GET',
    loading: {
      show: false,
    },
  });
};

/**
 * 获取微信手机号
 * @param code 微信小程序手机号码code
 * @returns
 */
export const getWxPhone = (code: string) => {
  return request({
    url: `/api/wx/login/getWxPhone?code=${code}`,
    method: 'GET',
    loading: {
      show: false,
    },
  });
};

/**
 * 用户实名认证
 */
export const userAuth = (data: any) => {
  return request({
    url: `/api/ocr/silencen/auth`,
    method: 'POST',
    data,
    loading: {
      show: false,
    },
  });
};

/**
 * 获取用户身份证信息
 */
export const getCurrentUserInfo = (openId: string) => {
  return request({
    url: `/api/wx/login/getHzInfo?openId=${openId}`,
    method: 'GET',
    loading: {
      show: false,
    },
  });
};
