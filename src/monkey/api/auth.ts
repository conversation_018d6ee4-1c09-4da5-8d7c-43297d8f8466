import request from '../request';
import monkey from '@/monkey';

/**
 * 身份证识别
 * @param image 身份证图片
 * @returns
 */
export const getOcrCardRecognition = (image: string) => {
  console.log('🚀 ~ getOcrCardRecognition ~ image:', image);
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: monkey.$config.target.BASEURL + '/api/ocr/card/recognition',
      filePath: image,
      name: 'file',
      header: {
        'Content-Type': 'multipart/form-data',
        Author: monkey.$stores.useUserStore().token,
      },
      success: (res) => {
        console.log('🚀 ~ returnnewPromise ~ res:', res);
        if (res.statusCode == 200) {
          const data = JSON.parse(res.data);
          resolve(data);
        } else reject(false);
      },
      fail: (err) => {
        reject(false);
      },
    });
  });
};

/**
 * 上传图片
 * @param image 图片
 * @returns
 */
export const uploadImage = (image: string) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: monkey.$config.target.BASEURL + '/api/ocr/card/uploadForSm',
      filePath: image,
      name: 'file',
      header: {
        'Content-Type': 'multipart/form-data',
        Author: monkey.$stores.useUserStore().token,
      },
      success: (res) => {
        console.log('🚀 ~ returnnewPromise ~ res:', res);
        if (res.statusCode == 200) {
          const data = JSON.parse(res.data);
          resolve(data);
        } else reject(false);
      },
      fail: (err) => {
        reject(false);
      },
    });
  });
};

/**
 * 获取认证Token
 * @param code - 授权码
 * @returns Promise<any>
 */
export const getAuthToken = (code: string) => {
  return request({
    url: `/api/login/getToken?code=${code}`,
    method: 'GET',
    loading: {
      show: false,
    },
  });
};

/**
 * 检查发布内容是否合规
 * @param content 内容
 * @returns Promise<any>
 */
export const checkPublishContent = (content: string) => {
  return request({
    url: `/api/api/verify/checkContent?content=${content}`,
    method: 'GET',
    token: 'no',
    loading: {
      title: '发布中，请稍后...',
    },
  });
};
