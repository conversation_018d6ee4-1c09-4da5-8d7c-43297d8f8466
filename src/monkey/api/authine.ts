import request from '../request';
import { AuthineTypes } from '../types';

/**
 * 获取低代码列表
 * @param params 查询参数
 * @returns 低代码列表
 */
export const getAuthineList = (params: AuthineTypes.AuthineListParams) => {
  return request({
    url: '/api/api/runtime/query/list',
    method: 'POST',
    data: params,
    source: 'authine',
  });
};

export const getAuthineForm = (params: AuthineTypes.AuthineFormParams) => {
  return request({
    url: '/api/api/runtime/form/load',
    method: 'GET',
    data: params,
    source: 'authine',
  });
};