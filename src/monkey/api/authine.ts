import request from '../request';
import { AuthineTypes } from '../types';

/**
 * 获取低代码列表
 * @param params 查询参数
 * @returns 低代码列表
 */
export const getAuthineList = (params: AuthineTypes.AuthineListParams) => {
  return request({
    url: '/api/api/runtime/query/list',
    method: 'POST',
    data: params,
    source: 'authine',
  });
};

export const getAuthineForm = (params: AuthineTypes.AuthineFormParams) => {
  return request({
    url: '/api/api/runtime/form/load',
    method: 'GET',
    data: params,
    source: 'authine',
  });
};

export const deleteAuthineData = (params: AuthineTypes.AuthineFormDeleteParams) => {
  return request({
    url: '/api/api/runtime/query/delete_data',
    method: 'POST',
    data: params,
    source: 'authine',
  });
};

export const submitAuthineForm = (params: AuthineTypes.AuthineFormSubmitParams) => {
  return request({
    url: '/api/api/runtime/form/submitForXcx',
    method: 'POST',
    data: params,
    source: 'all',
  });
};

export const getAuthineFormReplayToken = () => {
  return request({
    url: '/api/api/runtime/form/getReplayToken',
    method: 'GET',
    source: 'authine',
  });
};

export const getEnableRecordsByDictionaryId = (dicId: string) => {
  return request({
    url: `/api/api/data_dictionary/getEnableRecordsByDictionaryId?dicId=${dicId}`,
    method: 'GET',
    source: 'authine',
  });
};
