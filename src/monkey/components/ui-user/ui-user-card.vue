<template>
  <div class="flex items-center justify-between mb-5 mt-8">
    <div class="flex items-center">
      <div class="relative h-14 w-14 bg-white/20 rounded-full flex items-center justify-center mr-3 backdrop-blur-sm border border-white/30 shadow-xl">
        <div class="h-10 w-10 bg-white/30 rounded-full flex items-center justify-center">
          <div v-if="hasLogin" class="i-mdi-account-tie text-3xl text-white"></div>
          <div v-else class="i-mdi-account-tie-outline text-3xl text-gray-400"></div>
        </div>
        <!-- 在线状态指示器 -->
        <div v-if="hasLogin" class="absolute -bottom-0.5 -right-0.5 h-4 w-4 bg-green-400 rounded-full border-2 border-white shadow-md">
          <div class="h-full w-full bg-green-400 rounded-full animate-ping"></div>
        </div>
        <div v-else class="absolute -bottom-0.5 -right-0.5 h-4 w-4 bg-red-400 rounded-full border-2 border-white shadow-md">
          <div class="h-full w-full bg-red-400 rounded-full animate-ping"></div>
        </div>
      </div>
      <div>
        <div v-if="hasLogin" class="flex items-center gap-2 mb-0.5">
          <span class="text-base font-bold text-white">{{ userInfo?.zsxm || userInfo?.nc }}</span>
          <!-- 认证标志 - 统一样式 -->
          <div :class="getAuthStatusBgClass(userInfo?.sfrz)" class="flex items-center gap-1 px-2 py-0.5 rounded-full">
            <text :class="getAuthStatusIconClass(userInfo?.sfrz)" class="text-xs text-white"></text>
            <span class="text-xs text-white font-medium">{{ monkey.$helper.utils.getAuthStatusText(userInfo?.sfrz) }}</span>
          </div>
        </div>
        <div v-else class="text-base font-bold mb-0.5 text-white" @click="handleLogin">点击登录</div>
        <div class="flex items-center text-xs text-blue-100" v-if="hasLogin">
          <div>{{ monkey.$helper.utils.hidePhone(userInfo.sjh) }}</div>
        </div>
      </div>
    </div>
    <div class="flex items-center space-x-2" v-if="hasLogin">
      <!--
         <div class="h-8 w-8 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-md">
            <div class="text-sm">🔔</div>
          </div>
        -->
      <div class="h-8 w-8 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-md" @click="monkey.$router.navigateTo('/modules/user/setting/index')">
        <text class="i-mdi-cog-outline text-xl text-white"></text>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import monkey from '@/monkey';

  // 获取用户状态和登录弹窗方法
  const userStore = monkey.$stores.useUserStore();
  const { user: userInfo, hasLogin } = storeToRefs(userStore);
  const { open: openAuthModal } = monkey.$stores.useAuthModalStore();

  /**
   * 获取认证状态背景色
   * @param status 认证状态
   * @returns 背景色
   */
  const getAuthStatusBgClass = (status: number): string => {
    return status === 0 ? 'bg-orange-500/90' : status === 1 ? 'bg-green-500/90' : 'bg-gray-500/90';
  };

  /**
   * 获取认证状态图标
   * @param status 认证状态
   * @returns 图标
   */
  const getAuthStatusIconClass = (status: number): string => {
    return status === 0 ? 'i-mdi-shield-alert' : status === 1 ? 'i-mdi-shield-check' : 'i-mdi-clock-time-eight-outline';
  };

  /**
   * 处理登录点击
   */
  const handleLogin = () => {
    openAuthModal();
  };
</script>
<style lang="scss" scoped></style>
