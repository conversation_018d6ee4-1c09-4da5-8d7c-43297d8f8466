<template>
  <div class="grid grid-cols-1 shadow-sm rounded-xl overflow-hidden bg-white">
    <div class="px-3 py-2">
      <div class="flex items-center space-x-2" @click="monkey.$router.navigateTo(`/modules/user/auth/company/index`)">
        <div class="h-8 w-8 flex items-center justify-center">
          <text class="i-mdi-city text-2xl text-theme-blue-light"></text>
        </div>
        <div class="flex-1 min-w-0 flex">
          <div class="text-sm text-gray-800">企业认证</div>
        </div>
        <div class="flex items-center justify-center">
          <div class="text-xs text-gray-500" v-if="hasLogin">未认证</div>
          <text class="i-mdi-chevron-right text-lg text-gray-500"></text>
        </div>
      </div>
    </div>
    <div class="border-b border-gray-100 bg-gray-50 w-5/6 mx-auto"></div>
    <div class="px-3 py-2">
      <div class="flex items-center space-x-2" @click="handleAuth">
        <div class="h-8 w-8 flex items-center justify-center">
          <text class="i-mdi-shield-account-variant text-2xl text-theme-blue-light"></text>
        </div>
        <div class="flex-1 min-w-0 flex">
          <div class="text-sm text-gray-800">个人认证</div>
        </div>
        <div class="flex items-center justify-center">
          <div class="text-xs flex gap-2" v-if="hasLogin">
            <text class="text-theme-blue-light" :class="monkey.$helper.utils.getAuthStatusClass(userInfo.sfrz)">{{ monkey.$helper.utils.getAuthStatusText(userInfo.sfrz) }}</text>
          </div>
          <text class="i-mdi-chevron-right text-lg text-gray-500"></text>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  const { user: userInfo, hasLogin } = storeToRefs(monkey.$stores.useUserStore());

  /**
   * 处理认证
   */
  const handleAuth = () => {
    if (userInfo.value?.sfrz == 1 || userInfo.value?.sfrz == 2) return monkey.$router.navigateTo(`/modules/user/auth/person/certified`);
    monkey.$router.navigateTo(`/modules/user/auth/person/index`);
  };
</script>

<style scoped lang="scss"></style>
