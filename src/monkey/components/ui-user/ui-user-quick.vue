<template>
  <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
    <div class="flex items-center mb-2">
      <ly-title title="快捷功能" />
    </div>
    <div class="grid grid-cols-4 gap-2">
      <div
        v-for="service in quickServices"
        :key="service.name"
        @click="handleServiceClick(service)"
        class="flex flex-col justify-center items-center gap-1 py-1.5 active:shadow-md active:bg-gradient-to-r active:bg-gray-50 active:text-theme-blue-light active:rounded-lg transition-all"
      >
        <div class="h-8 w-8 rounded-lg flex items-center justify-center text-white">
          <text :class="service.icon" class="text-theme-blue-light text-2xl"></text>
        </div>
        <div class="flex-1 min-w-0">
          <div class="text-gray-800 text-sm">
            {{ service.name }}
          </div>
          <!-- <div class="text-[10px] text-gray-500 ">
            {{ service.desc }}
          </div> -->
        </div>
        <!-- <text class="i-mdi-chevron-right text-xs text-gray-500"></text> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  interface Service {
    name: string;
    desc: string;
    icon: string;
    path: string;
  }

  // 快捷功能
  const quickServices = reactive<Service[]>([
    {
      name: '入库记录',
      desc: '入库预约记录',
      icon: 'i-mdi-invoice-text-arrow-left',
      path: '/modules/user/inbound/index',
    },
    {
      name: '出库记录',
      desc: '出库预约记录',
      icon: 'i-mdi-invoice-text-arrow-right',
      path: '/modules/user/outbound/index',
    },
    // {
    //   name: '财务报表',
    //   desc: '收支统计分析',
    //   icon: '📊',
    // },
    // {
    //   name: '质检报告',
    //   desc: '产品质量检测',
    //   icon: '🔍',
    // },
  ]);

  const handleServiceClick = (service: Service) => {
    monkey.$router.navigateTo(service.path);
  };
</script>
