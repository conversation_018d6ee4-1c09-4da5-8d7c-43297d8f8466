<template>
  <van-popup :root-portal="rootPortal" :safe-area-inset-bottom="safeBottom" v-model:show="show" :position="position" @click-overlay="handleMaskClick" round :z-index="zIndex">
    <div v-if="title" class="px-4 py-3 border-b border-gray-200 text-base font-bold text-gray-800 flex items-center justify-between gap-2">
      <text>{{ title }}</text>
      <text class="i-mdi-close text-gray-500 text-base" @click="close"></text>
    </div>
    <slot></slot>
  </van-popup>
</template>
<script setup lang="ts">
  /**
   * 弹窗组件
   * @typedef {Object} Props
   * @property {boolean} show - 是否显示弹窗
   * @property {string} title - 弹窗标题
   * @property {'bottom' | 'top' | 'left' | 'right'} position - 弹窗位置
   * @property {number} [zIndex=1000] - z-index 值
   * @property {boolean} [rootPortal=true] - 是否使用根节点渲染
   * @property {boolean} [safeBottom=true] - 是否适配安全区域
   */
  interface Props {
    show: boolean;
    title: string;
    position: 'bottom' | 'top' | 'left' | 'right';
    zIndex?: number;
    rootPortal?: boolean;
    safeBottom?: boolean;
  }

  const props = withDefaults(
    defineProps<Props>(),
    {
      zIndex: 1000,
      rootPortal: true,
      safeBottom: true,
    },
  );

  const emit = defineEmits<{
    (e: 'close'): void;
    (e: 'update:show', value: boolean): void;
  }>();

  const close = () => {
    emit('update:show', false);
    emit('close');
  };

  const handleMaskClick = () => {
    close();
  };
</script>
