<template>
  <div class="grid grid-cols-4 gap-4">
    <div @click="handleActionClick(item)" v-for="item in actions" :key="item.title" class="justify-center gap-2 whitespace-nowrap h-auto px-4 flex flex-col items-center rounded-lg">
      <div :class="item.color" class="h-10 w-10 rounded-lg flex items-center justify-center">
        <text :class="item.icon"></text>
      </div>
      <div class="text-center">
        <div class="font-medium text-gray-800 text-xs">{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import monkey  from '@/monkey';
  import type { Action } from '@/monkey/types';

  // 数据
  const actions: Action[] = [
    {
      title: '入库预约',
      icon: 'icon-peijianrukutongji iconfont text-xl text-white',
      color: 'bg-gradient-to-br from-blue-500 to-blue-600',
      path: '/modules/home/<USER>/index',
    },
    {
      title: '出库预约',
      icon: 'icon-peijianchukutongji iconfont text-xl text-white',
      color: 'bg-gradient-to-br from-cyan-500 to-cyan-600',
      path: '/modules/home/<USER>/index',
    },
    {
      title: '我的仓库',
      icon: 'icon-cangkuguanli-cangkuguanli iconfont text-xl text-white',
      color: 'bg-gradient-to-br from-sky-500 to-sky-600',
      path: '/modules/home/<USER>/index',
    },
    {
      title: '货运服务',
      icon: 'icon-huoyunqiye iconfont text-xl text-white',
      color: 'bg-gradient-to-br from-indigo-500 to-indigo-600', 
      path: '/modules/home/<USER>/index',
    },
  ];

  const handleActionClick = (item: Action) => {
    if (item.path) {
      monkey.$router.navigateTo(item.path);
    }
  };
</script>

<style lang="scss" scoped></style>
