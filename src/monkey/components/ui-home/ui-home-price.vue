<template>
  <div class="rounded-lg bg-white shadow-md px-4 py-5">
    <slot name="title"></slot>
    <div class="w-full flex flex-col items-center">
      <div class="grid grid-cols-3 gap-3 pb-4 mb-4 border-b border-gray-100 w-full">
        <div class="text-xs font-medium text-gray-600 text-center">品名</div>
        <!-- <div class="text-xs font-medium text-gray-600 text-center">市场</div> -->
        <div class="text-xs font-medium text-gray-600 text-center">价格</div>
        <!-- <div class="text-xs font-medium text-gray-600 text-center">涨跌幅</div> -->
      </div>
      <swiper class="w-full" :autoplay="true" :duration="3000" :interval="1500" :circular="true" :vertical="true" :display-multiple-items="4" :indicator-dots="true" indicator-color="#e0e0e0" indicator-active-color="#007aff">
        <swiper-item v-for="item in list" :key="item.id" @click="handleClick(item)">
          <div class="grid grid-cols-3 gap-3 items-center rounded-lg active:bg-gray-50/70 transition-colors border-b border-gray-100 last:border-b-0 pb-3 last:pb-0">
            <div class="text-center flex flex-col items-center gap-0.5">
              <div class="truncate text-xs text-gray-800">{{ item.ycmc }}</div>
            </div>
            <div class="text-xs font-bold text-center">{{ item.jg }}</div>
          </div>
        </swiper-item>
      </swiper>
    </div>
  </div>
</template>
<script setup lang="ts">
  import monkey from '@/monkey';
  import type { MedicineTypes } from '@/monkey/types';

  const props = defineProps<{
    list: MedicineTypes.MedicinalCurrentItemDetail[];
  }>();

  /**
   * 点击药材行情
   * @param item 药材行情
   */
  const handleClick = (item: MedicineTypes.MedicinalCurrentItemDetail) => {
    monkey.$router.navigateTo(`/modules/home/<USER>/detail?ycbm=${item.glyc}`);
  };
</script>
