<template>
  <div class="grid grid-cols-2 gap-4 overflow-hidden">
    <div class="relative overflow-hidden">
      <div
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background active:scale-98 w-full h-[74px] text-gray-800 font-bold rounded-lg shadow-xl border-0 p-0 relative overflow-hidden group transition-all duration-300"
        style="background: linear-gradient(135deg, rgb(179, 217, 242), rgb(128, 199, 237), rgb(77, 166, 224))"
      >
        <div class="absolute inset-0 bg-gradient-to-r from-white/30 to-transparent"></div>
        <div class="absolute top-0 right-0 w-20 h-20 bg-white/5 rounded-full -translate-y-6 translate-x-6"></div>
        <div class="absolute top-4 right-8 w-8 h-8 bg-white/15 rounded-full"></div>
        <div class="absolute bottom-0 left-0 w-16 h-16 bg-white/15 rounded-full translate-y-6 -translate-x-6"></div>
        <div class="absolute bottom-4 left-8 w-4 h-4 bg-white/20 rounded-full"></div>
        <div class="absolute top-6 left-4 w-12 h-0.5 bg-white/25 rounded-full rotate-12"></div>
        <div class="absolute bottom-8 right-6 w-8 h-0.5 bg-white/25 rounded-full -rotate-12"></div>
        <div class="absolute top-8 right-12 w-3 h-3 bg-white/20 rotate-45"></div>
        <div class="absolute bottom-12 left-12 w-2 h-2 bg-white/25 rotate-45"></div>
        <div class="relative z-10 flex items-center w-full px-4">
          <div class="flex items-center space-x-4">
            <div class="h-10 w-10 bg-white/30 rounded-lg flex items-center justify-center backdrop-blur-sm border border-white/20 shadow-lg">
              <text class="iconfont icon-mairu !text-2xl text-[#006dbb]"></text>
            </div>
            <div class="text-left">
              <p class="text-base font-bold text-[#006dbb]">我要买</p>
              <p class="text-xs font-medium text-[#0080d4]">采购管理</p>
            </div>
          </div>
        </div>
        <div class="absolute top-0 left-0 w-full h-full">
          <div class="absolute top-3 left-3 w-1 h-1 bg-white/40 rounded-full animate-pulse"></div>
          <div class="absolute top-12 left-16 w-1 h-1 bg-white/40 rounded-full animate-pulse delay-300"></div>
          <div class="absolute bottom-6 right-4 w-1 h-1 bg-white/40 rounded-full animate-pulse delay-700"></div>
        </div>
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
      </div>
    </div>
    <div class="relative overflow-hidden">
      <div
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background active:scale-98 w-full h-[74px] text-gray-800 font-bold rounded-lg shadow-xl border-0 p-0 relative overflow-hidden group transition-all duration-300"
        style="background: linear-gradient(135deg, rgb(77, 166, 224), rgb(0, 128, 212), rgb(0, 109, 187))"
      >
        <div class="absolute inset-0 bg-gradient-to-l from-white/30 to-transparent"></div>
        <div class="absolute top-0 left-0 w-20 h-20 bg-white/5 rounded-full -translate-y-6 -translate-x-6"></div>
        <div class="absolute top-4 left-8 w-8 h-8 bg-white/15 rounded-full"></div>
        <div class="absolute bottom-0 right-0 w-16 h-16 bg-white/15 rounded-full translate-y-6 translate-x-6"></div>
        <div class="absolute bottom-4 right-8 w-4 h-4 bg-white/20 rounded-full"></div>
        <div class="absolute top-6 right-4 w-12 h-0.5 bg-white/25 rounded-full -rotate-12"></div>
        <div class="absolute bottom-8 left-6 w-8 h-0.5 bg-white/25 rounded-full rotate-12"></div>
        <div class="absolute top-8 left-12 w-3 h-3 bg-white/20 rotate-45"></div>
        <div class="absolute bottom-12 right-12 w-2 h-2 bg-white/25 rotate-45"></div>
        <div class="relative z-10 flex items-center w-full px-4">
          <div class="flex items-center space-x-4">
            <div class="h-10 w-10 bg-white/30 rounded-lg flex items-center justify-center backdrop-blur-sm border border-white/20 shadow-lg">
              <text class="iconfont icon-maichu !text-2xl text-white"></text>
            </div>
            <div class="text-left">
              <p class="text-base font-bold text-white">我要卖</p>
              <p class="text-xs text-blue-100 font-medium">销售管理</p>
            </div>
          </div>
        </div>
        <div class="absolute top-0 left-0 w-full h-full">
          <div class="absolute top-3 right-3 w-1 h-1 bg-white/40 rounded-full animate-pulse"></div>
          <div class="absolute top-12 right-16 w-1 h-1 bg-white/40 rounded-full animate-pulse delay-300"></div>
          <div class="absolute bottom-6 left-4 w-1 h-1 bg-white/40 rounded-full animate-pulse delay-700"></div>
        </div>
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts"></script>

<style lang="scss" scoped></style>
