<template>
  <!-- 主要功能网格 -->
  <div class="grid grid-cols-2 grid-rows-2 gap-4 h-48">
    <!-- 左侧大卡片: 药材行情 -->
    <div @click="handleClick(0)" class="active:bg-gray-100 row-span-2 border border-1 border-gray-100 shadow-sm col-span-1 bg-white rounded-lg p-4 flex flex-col relative card-gradient">
      <div class="z-10">
        <div class="text-gray-800 flex items-center gap-1">
          <div class="font-bold text-base">药材行情</div>
          <div class="i-mdi-chevron-right text-xl"></div>
        </div>
        <div class="text-xs text-gray-500 mt-1">实时市场价格监控</div>
      </div>
      <div class="flex-grow w-full h-full flex items-end justify-center z-0">
        <image src="@/static/images/home-ychq.png" class="w-28 h-28"></image>
      </div>
    </div>

    <!-- 右上: 入库预约 -->
    <div @click="handleClick(1)" class="active:bg-gray-100 col-span-1 overflow-hidden border border-1 border-gray-100 shadow-sm bg-white rounded-lg p-3 flex flex-col relative card-gradient">
      <div class="z-10">
        <div class="text-gray-800 flex items-center gap-1">
          <div class="font-bold text-base">入库预约</div>
          <div class="i-mdi-chevron-right text-xl"></div>
        </div>
        <div class="text-xs text-gray-500 mt-1">扫码快速入库</div>
      </div>
      <div class="flex-grow w-full flex items-end justify-end z-0 absolute -bottom-4 right-0">
        <image src="@/static/images/home-rkyy.png" class="w-16 h-16"></image>
      </div>
    </div>

    <!-- 右下: 出库预约 -->
    <div @click="handleClick(2)" class="active:bg-gray-100 col-span-1 overflow-hidden border border-1 border-gray-100 shadow-sm bg-white rounded-lg p-3 flex flex-col relative card-gradient">
      <div class="z-10">
        <div class="text-gray-800 flex items-center gap-1">
          <div class="font-bold text-base">出库预约</div>
          <div class="i-mdi-chevron-right text-xl"></div>
        </div>
        <div class="text-xs text-gray-500 mt-1">智能调度出库</div>
      </div>
      <div class="flex-grow w-full h-full flex items-end justify-end z-0 absolute -bottom-2 right-0">
        <image src="@/static/images/home-ckyy.png" class="w-16 h-16"></image>
      </div>
    </div>
  </div>

  <!-- 下方两个按钮 -->
  <div class="grid grid-cols-2 gap-4 mt-4 h-24">
    <!-- 看货 -->
    <div @click="handleClick(3)" class="active:bg-gray-100 col-span-1 overflow-hidden border border-1 border-gray-100 shadow-sm bg-white rounded-lg p-3 flex flex-col relative card-gradient">
      <div class="z-10">
        <div class="text-gray-800 flex items-center gap-1">
          <div class="font-bold text-base">看货服务</div>
          <div class="i-mdi-chevron-right text-xl"></div>
        </div>
        <div class="text-xs text-gray-500 mt-1">便捷看货体验</div>
      </div>
      <div class="flex-grow w-full flex items-end justify-end z-0 absolute -bottom-6 right-0">
        <image src="@/static/images/home-khfw.png" class="w-16 h-16"></image>
      </div>
    </div>
    <!-- 货款 -->
    <div @click="handleClick(4)" class="active:bg-gray-100 col-span-1 overflow-hidden border border-1 border-gray-100 shadow-sm bg-white rounded-lg p-3 flex flex-col relative card-gradient">
      <div class="z-10">
        <div class="text-gray-800 flex items-center gap-1">
          <div class="font-bold text-base">金融服务</div>
          <div class="i-mdi-chevron-right text-xl"></div>
        </div>
        <div class="text-xs text-gray-500 mt-1">货物仓单质押</div>
      </div>
      <div class="flex-grow w-full flex items-end justify-end z-0 absolute -bottom-6 right-0">
        <image src="@/static/images/home-jrfw.png" class="w-16 h-16"></image>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  const handleClick = (index: number) => {
    console.log('🚀 ~ handleClick ~ index:', index);
    switch (index) {
      case 0:
        monkey.$router.navigateTo('/modules/home/<USER>/index');
        break;
      case 1:
        monkey.$router.navigateTo('/modules/reservation/inbound/index');
        break;
      case 2:
        monkey.$router.navigateTo('/modules/home/<USER>/index');
        break;
      case 3:
        // 看货服务功能待实现
        console.log('看货服务功能待实现');
        break;
      case 4:
        // 金融服务功能待实现
        console.log('金融服务功能待实现');
        break;
    }
  };
</script>

<style lang="scss" scoped>
  .card-gradient::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60%;
    background: linear-gradient(to top, rgba(235, 245, 255, 0.6), transparent);
    pointer-events: none;
  }
</style>
