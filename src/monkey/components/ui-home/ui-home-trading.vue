<template>
  <div class="bg-white rounded-lg border border-gray-100 shadow-sm p-4">
    <slot name="title"></slot>

    <!-- 主要数据展示 -->
    <div class="grid grid-cols-2 gap-4 mb-3">
      <div class="text-center relative">
        <div class="text-lg font-bold mb-1 flex items-center justify-center">
          <span class="mr-1 text-red-500">{{ formatNumber(tradingData.totalAmount) }}</span>
          <span class="text-xs text-red-500">↑</span>
        </div>
        <div class="text-xs text-gray-500">累计交易额 (万元)</div>
        <div class="absolute right-0 top-1/2 -translate-y-1/2 w-px h-8 bg-gradient-to-b from-transparent via-gray-200 to-transparent"></div>
      </div>
      <div class="text-center">
        <div class="text-lg font-bold mb-1 flex items-center justify-center">
          <span class="mr-1 text-red-500">{{ formatNumber(tradingData.totalVolume) }}</span>
          <span class="text-xs text-red-500">↑</span>
        </div>
        <div class="text-xs text-gray-500">累计交易量 (吨)</div>
      </div>
    </div>

    <!-- 数据统计 -->
    <div class="grid grid-cols-3 gap-2 mb-3 px-3 py-2 bg-gray-50 rounded-lg">
      <div class="text-center">
        <div class="text-base font-semibold text-gray-700">{{ formatNumber(tradingData.orderCount) }}</div>
        <div class="text-xs text-gray-500">订单数量</div>
      </div>
      <div class="text-center relative">
        <div class="text-base font-semibold text-gray-700">{{ tradingData.categoryCount }}</div>
        <div class="text-xs text-gray-500">上线品种</div>
        <div class="absolute left-0 top-1/2 -translate-y-1/2 w-px h-6 bg-gray-200"></div>
        <div class="absolute right-0 top-1/2 -translate-y-1/2 w-px h-6 bg-gray-200"></div>
      </div>
      <div class="text-center">
        <div class="text-base font-semibold text-gray-700">{{ formatNumber(tradingData.merchantCount) }}</div>
        <div class="text-xs text-gray-500">注册商家</div>
      </div>
    </div>

    <!-- 交易动态 -->
    <div class="border-t border-gray-100 pt-3">
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
          <div class="text-xs font-medium text-gray-700">实时交易动态</div>
        </div>
        <div class="text-xs text-gray-400">实时更新</div>
      </div>

      <div class="space-y-2 max-h-20 overflow-hidden">
        <div v-for="(item, index) in tradingList" :key="index" class="flex items-center text-xs transition-all duration-300" :class="{ 'opacity-60': index > 0 }">
          <div class="w-1 h-4 bg-gradient-to-b from-green-500 to-transparent rounded-full mr-2 flex-shrink-0"></div>
          <div class="flex-1 min-w-0">
            <div class="flex justify-between items-center">
              <span class="text-gray-600 truncate">{{ item.description }}</span>
              <span class="text-slate-600 ml-2 font-medium whitespace-nowrap">￥{{ item.amount }}万</span>
            </div>
            <div class="flex justify-between items-center mt-0.5">
              <span class="text-gray-400">{{ item.time }}</span>
              <span class="text-slate-600 px-1.5 py-0.5 rounded-full text-xs font-medium">{{ item.status }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';

  // 交易数据
  const tradingData = ref({
    totalAmount: 74047,
    totalVolume: 10940,
    orderCount: 1167,
    categoryCount: 122,
    merchantCount: 6239,
  });

  // 交易动态列表
  const tradingList = ref([
    {
      description: '张**采购花生139kg',
      amount: '12.07',
      time: '刚刚',
      status: '已成交',
    },
  ]);

  // 格式化数字显示
  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  // 模拟实时数据更新
  let updateInterval: NodeJS.Timeout | null = null;

  const updateTradingData = () => {
    // 模拟数据小幅波动
    tradingData.value.totalAmount += Math.floor(Math.random() * 10) + 1;
    tradingData.value.totalVolume += Math.floor(Math.random() * 5) + 1;
    tradingData.value.orderCount += Math.floor(Math.random() * 3) + 1;

    // 模拟新的交易动态
    const newTrading = {
      description: getRandomTrading(),
      amount: (Math.random() * 20 + 5).toFixed(2),
      time: '刚刚',
      status: '已成交',
    };

    // 更新时间
    tradingList.value.forEach((item, index) => {
      if (index === 0) item.time = '1分钟前';
      else if (index === 1) item.time = '2分钟前';
      else if (index === 2) item.time = '3分钟前';
    });

    // 添加新交易到列表顶部
    tradingList.value.unshift(newTrading);

    // 保持最多3条记录
    if (tradingList.value.length > 3) {
      tradingList.value.pop();
    }
  };

  const getRandomTrading = (): string => {
    const names = ['张**', '李**', '王**', '刘**', '陈**', '赵**'];
    const actions = ['采购', '出售'];
    const products = ['花生', '白芍', '当归', '枸杞', '黄芪', '甘草', '人参', '三七'];

    const name = names[Math.floor(Math.random() * names.length)];
    const action = actions[Math.floor(Math.random() * actions.length)];
    const product = products[Math.floor(Math.random() * products.length)];
    const weight = Math.floor(Math.random() * 200) + 50;

    return `${name}${action}${product}${weight}kg`;
  };

  onMounted(() => {
    // 每30秒更新一次数据
    updateInterval = setInterval(updateTradingData, 30000);
  });

  onUnmounted(() => {
    if (updateInterval) {
      clearInterval(updateInterval);
    }
  });
</script>

<style scoped>
  /* 完全使用 Tailwind CSS，无需自定义样式 */
</style>
