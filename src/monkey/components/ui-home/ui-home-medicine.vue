<template>
  <div class="rounded-lg bg-white shadow-md p-4">
    <!-- 药材行情展示区 -->
    <div class="space-y-3">
      <!-- 药材品种选择器 -->
      <div class="flex space-x-2 mb-4 overflow-x-auto pb-1 hide-scrollbar">
        <div 
          v-for="(herb, index) in hotHerbs" 
          :key="index"
          class="px-3 py-1.5 bg-blue-50 text-blue-600 rounded-full text-xs whitespace-nowrap cursor-pointer"
          :class="{'bg-blue-600 text-white': index === 0}"
        >
          {{herb}}
        </div>
      </div>
      
      <!-- 药材市场数据表格 -->
      <div class="w-full overflow-hidden">
        <div class="grid grid-cols-4 gap-2 pb-2 mb-2 border-b border-gray-100">
          <div class="text-xs font-medium text-gray-600">品名/规格</div>
          <div class="text-xs font-medium text-gray-600 text-center">产地</div>
          <div class="text-xs font-medium text-gray-600 text-center">价格</div>
          <div class="text-xs font-medium text-gray-600 text-center">涨跌幅</div>
        </div>
        
        <div class="space-y-3">
          <div 
            v-for="(item, index) in medicineList" 
            :key="index"
            class="grid grid-cols-4 gap-2 items-center py-2 border-b border-gray-100 last:border-b-0"
          >
            <div>
              <div class="text-sm text-gray-800 font-medium">{{item.name}}</div>
              <div class="text-xs text-gray-500">{{item.spec}}</div>
            </div>
            <div class="text-xs text-gray-600 text-center">{{item.origin}}</div>
            <div class="text-sm font-bold text-center">¥{{item.price}}</div>
            <div class="flex justify-center">
              <span 
                class="inline-block px-2 py-1 rounded-full text-xs font-medium"
                :class="{
                  'text-green-600 bg-green-50': isPositive(item.change),
                  'text-red-600 bg-red-50': isNegative(item.change),
                  'text-gray-500': isZero(item.change)
                }"
              >
                {{item.change}}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 热门药材列表
const hotHerbs = [
  '川芎', '党参', '白术', '黄芪', '三七', '丹参', '太子参', '红参', '西洋参'
];

// 药材数据列表
const medicineList = [
  {
    name: '川芎',
    spec: '统货',
    origin: '四川产区',
    price: 45.8,
    change: '+4.5%',
  },
  {
    name: '党参',
    spec: '统货',
    origin: '甘肃岷县',
    price: 62.5,
    change: '-1.2%',
  },
  {
    name: '白术',
    spec: '一级',
    origin: '浙江产区',
    price: 38.2,
    change: '+0.0%',
  },
  {
    name: '黄芪',
    spec: '特级',
    origin: '内蒙古',
    price: 72.5,
    change: '+2.3%',
  },
];

// 判断涨跌幅是否为正数、负数或零
const isPositive = (change: string) =>
  change.startsWith('+') && change !== '+0.00%'
const isNegative = (change: string) =>
  change.startsWith('-') && change !== '-0.00%'
const isZero = (change: string) =>
  change === '+0.00%' || change === '-0.00%' || change === '0.00%'
</script>

<style scoped>
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style> 