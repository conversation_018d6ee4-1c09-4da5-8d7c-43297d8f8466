<template>
  <div class="grid grid-cols-2 gap-4">
    <div class="relative overflow-hidden rounded-lg shadow-md group transition-all duration-300 bg-white border border-l-4 border-theme-blue">
      <div class="absolute inset-0 bg-gradient-to-r from-white/30 to-transparent"></div>
      <div class="absolute top-0 right-0 w-12 h-12 bg-white/5 rounded-full -translate-y-4 translate-x-4"></div>
      <div class="absolute bottom-0 left-0 w-10 h-10 bg-white/15 rounded-full translate-y-4 -translate-x-4"></div>
      <div class="absolute top-3 left-2 w-8 h-0.5 bg-white/25 rounded-full rotate-12"></div>
      <div class="relative z-10 p-4">
        <div class="flex items-center space-x-4">
          <div class="h-10 w-10 bg-green-50 rounded-lg flex items-center justify-center">
            <text class="iconfont icon-mairu !text-xl text-theme-blue"></text>
          </div>
          <div>
            <div class="text-sm font-bold text-gray-800 mb-1">我去采购</div>
            <div class="text-xs text-gray-500">采购管理</div>
          </div>
        </div>
      </div>
    </div>
    <div class="relative overflow-hidden rounded-lg shadow-md group transition-all duration-300 bg-white border border-l-4 border-theme-blue">
      <div class="absolute inset-0 bg-gradient-to-l from-white/30 to-transparent"></div>
      <div class="absolute top-0 left-0 w-12 h-12 bg-white/5 rounded-full -translate-y-4 -translate-x-4"></div>
      <div class="absolute bottom-0 right-0 w-10 h-10 bg-white/15 rounded-full translate-y-4 translate-x-4"></div>
      <div class="absolute top-3 right-2 w-8 h-0.5 bg-white/25 rounded-full -rotate-12"></div>
      <div class="relative z-10 p-4">
        <div class="flex items-center space-x-4">
          <div class="h-10 w-10 bg-green-50 rounded-lg flex items-center justify-center">
            <text class="iconfont icon-maichu !text-xl text-theme-blue"></text>
          </div>
          <div>
            <div class="text-sm font-bold text-gray-800 mb-1">我去销售</div>
            <div class="text-xs text-gray-500">销售管理</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped></style>
