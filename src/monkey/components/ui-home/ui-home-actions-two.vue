<template>
  <!-- 主要功能网格 -->
  <div class="grid grid-cols-2 grid-rows-2 gap-3 h-36">
    <!-- 左侧大卡片: 药材行情 -->
    <div @click="handleClick(0)" class="row-span-2 col-span-1 bg-white rounded-lg border border-gray-100 shadow-sm p-3 flex flex-col relative overflow-hidden transition-all duration-200 hover:shadow-md active:scale-95">
      <div class="relative z-10">
        <div class="text-gray-800 flex items-center gap-1">
          <div class="font-bold text-sm">药材行情</div>
          <div class="i-mdi-chevron-right text-lg text-gray-400"></div>
        </div>
        <div class="text-xs text-gray-500 mt-0.5">实时市场价格监控</div>
      </div>
      <div class="flex-grow w-full flex items-end justify-center mt-2">
        <image src="@/static/images/ychq.png" class="w-20 h-20 object-contain"></image>
      </div>
      <div class="absolute inset-0 bg-gradient-to-t from-green-50/50 to-transparent pointer-events-none"></div>
    </div>

    <!-- 右上: 入库预约 -->
    <div @click="handleClick(1)" class="bg-white rounded-lg border border-gray-100 shadow-sm p-3 flex flex-col relative overflow-hidden transition-all duration-200 hover:shadow-md active:scale-95">
      <div class="relative z-10">
        <div class="text-gray-800 flex items-center gap-1">
          <div class="font-bold text-sm">入库预约</div>
          <div class="i-mdi-chevron-right text-lg text-gray-400"></div>
        </div>
        <div class="text-xs text-gray-500 mt-0.5">扫码快速入库</div>
      </div>
      <div class="absolute -bottom-2 -right-2">
        <image src="@/static/images/rkyu.png" class="w-12 h-12 object-contain"></image>
      </div>
      <div class="absolute inset-0 bg-gradient-to-t from-green-50/50 to-transparent pointer-events-none"></div>
    </div>

    <!-- 右下: 出库预约 -->
    <div @click="handleClick(2)" class="bg-white rounded-lg border border-gray-100 shadow-sm p-3 flex flex-col relative overflow-hidden transition-all duration-200 hover:shadow-md active:scale-95">
      <div class="relative z-10">
        <div class="text-gray-800 flex items-center gap-1">
          <div class="font-bold text-sm">出库预约</div>
          <div class="i-mdi-chevron-right text-lg text-gray-400"></div>
        </div>
        <div class="text-xs text-gray-500 mt-0.5">智能调度出库</div>
      </div>
      <div class="absolute -bottom-1 -right-2">
        <image src="@/static/images/ckyu.png" class="w-12 h-12 object-contain"></image>
      </div>
      <div class="absolute inset-0 bg-gradient-to-t from-green-50/50 to-transparent pointer-events-none"></div>
    </div>
  </div>

  <!-- 下方两个按钮 -->
  <div class="grid grid-cols-2 gap-3 mt-3 h-20">
    <!-- 看货服务 -->
    <div @click="handleClick(3)" class="bg-white rounded-lg border border-gray-100 shadow-sm p-3 flex flex-col relative overflow-hidden transition-all duration-200 hover:shadow-md active:scale-95">
      <div class="relative z-10">
        <div class="text-gray-800 flex items-center gap-1">
          <div class="font-bold text-sm">看货服务</div>
          <div class="i-mdi-chevron-right text-lg text-gray-400"></div>
        </div>
        <div class="text-xs text-gray-500 mt-0.5">便捷看货体验</div>
      </div>
      <div class="absolute -bottom-3 -right-2">
        <image src="@/static/images/kh.png" class="w-12 h-12 object-contain"></image>
      </div>
      <div class="absolute inset-0 bg-gradient-to-t from-green-50/50 to-transparent pointer-events-none"></div>
    </div>

    <!-- 金融服务 -->
    <div @click="handleClick(4)" class="bg-white rounded-lg border border-gray-100 shadow-sm p-3 flex flex-col relative overflow-hidden transition-all duration-200 hover:shadow-md active:scale-95">
      <div class="relative z-10">
        <div class="text-gray-800 flex items-center gap-1">
          <div class="font-bold text-sm">金融服务</div>
          <div class="i-mdi-chevron-right text-lg text-gray-400"></div>
        </div>
        <div class="text-xs text-gray-500 mt-0.5">货物仓单质押</div>
      </div>
      <div class="absolute -bottom-3 -right-2">
        <image src="@/static/images/dk.png" class="w-12 h-12 object-contain"></image>
      </div>
      <div class="absolute inset-0 bg-gradient-to-t from-green-50/50 to-transparent pointer-events-none"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import monkey from '@/monkey';

const handleClick = (index: number) => {
  console.log("🚀 ~ handleClick ~ index:", index)
  switch (index) {
    case 0:
      monkey.$router.navigateTo('/modules/home/<USER>/index');
      break;
    case 1:
      monkey.$router.navigateTo('/modules/home/<USER>/index');
      break;
    case 2:
      monkey.$router.navigateTo('/modules/home/<USER>/index');
      break;
    case 3:
      // 看货服务功能待实现
      console.log('看货服务功能待实现');
      break;
    case 4:
      // 金融服务功能待实现
      console.log('金融服务功能待实现');
      break;
  }
};
</script>

<style scoped>
/* 所有样式已迁移至 Tailwind CSS */
</style>
