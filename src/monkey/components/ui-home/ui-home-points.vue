<template>
  <div class="bg-gradient-to-br from-white to-slate-50 rounded-lg p-px shadow-sm">
    <div class="bg-white rounded-lg py-3 flex items-center">
      <!-- 优惠券 -->
      <div class="flex-1 text-center cursor-pointer relative px-2 py-1 transition-transform duration-200 active:-translate-y-0.5" @click="handleCouponClick">
        <div class="flex flex-col items-center gap-0.5">
          <div class="w-6 h-6 rounded-full bg-red-50 flex items-center justify-center mb-0.5">
            <text class="i-mdi-ticket text-red-500 text-sm"></text>
          </div>
          <div class="text-lg font-bold text-slate-900 leading-tight mt-1">{{ couponCount || '0' }}</div>
          <div class="text-xs text-slate-500 font-medium">优惠券</div>
        </div>
        <div class="absolute right-0 top-1/2 -translate-y-1/2 w-px h-6 bg-gradient-to-b from-transparent via-slate-200 to-transparent"></div>
      </div>

      <!-- 余额 -->
      <div class="flex-1 text-center cursor-pointer relative px-2 py-1 transition-transform duration-200 active:-translate-y-0.5" @click="handleBalanceClick">
        <div class="flex flex-col items-center gap-0.5">
          <div class="w-6 h-6 rounded-full bg-green-50 flex items-center justify-center mb-0.5">
            <text class="i-mdi-wallet text-green-500 text-sm"></text>
          </div>
          <div class="text-lg font-bold text-slate-900 leading-tight mt-1">{{ balance || '0.00' }}</div>
          <div class="text-xs text-slate-500 font-medium">余额(元)</div>
        </div>
        <div class="absolute right-0 top-1/2 -translate-y-1/2 w-px h-6 bg-gradient-to-b from-transparent via-slate-200 to-transparent"></div>
      </div>

      <!-- 积分 -->
      <div class="flex-1 text-center cursor-pointer px-2 py-1 transition-transform duration-200 active:-translate-y-0.5" @click="handleScoreClick">
        <div class="flex flex-col items-center gap-0.5">
          <div class="w-6 h-6 rounded-full bg-orange-50 flex items-center justify-center mb-0.5">
            <text class="i-mdi-star text-orange-500 text-sm"></text>
          </div>
          <div class="text-lg font-bold text-slate-900 leading-tight mt-1">{{ points || '0' }}</div>
          <div class="text-xs text-slate-500 font-medium">积分</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  // 获取登录状态
  const { hasLogin } = storeToRefs(monkey.$stores.useUserStore());

  // 模拟数据
  const couponCount = computed(() => (hasLogin.value ? 5 : '---'));
  const balance = computed(() => (hasLogin.value ? '1,288.50' : '---'));
  const points = computed(() => (hasLogin.value ? 12580 : '---'));

  // 点击事件处理
  const handleCouponClick = () => {
    console.log('跳转到优惠券页面');
    monkey.$router.navigateTo('/modules/user/coupon/index');
  };

  const handleBalanceClick = () => {
    console.log('跳转到余额页面');
  };

  const handleScoreClick = () => {
    console.log('跳转到积分页面');
    monkey.$router.navigateTo('/modules/user/points/index');
  };
</script>

<style scoped>
  /* Tailwind CSS 已经提供了所有需要的样式 */
</style>
