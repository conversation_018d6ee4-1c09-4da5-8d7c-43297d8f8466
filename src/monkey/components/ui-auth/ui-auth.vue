<template>
  <van-popup v-model:show="visible" custom-style="background-color: transparent" @click-overlay="handleMaskClick" :safe-area-inset-bottom="false" position="bottom">
    <!-- 渐变装饰条 -->
    <div class="w-full h-7 bg-gradient-to-r from-theme-blue via-theme-blue-600 to-theme-blue rounded-t-2xl relative overflow-hidden">
      <!-- 装饰性光效 -->
      <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
      <!-- 小圆点装饰 -->
      <div class="absolute top-1/2 left-4 w-2 h-2 bg-white/30 rounded-full transform -translate-y-1/2"></div>
      <div class="absolute top-1/2 right-4 w-1.5 h-1.5 bg-white/40 rounded-full transform -translate-y-1/2"></div>
      <div class="absolute top-1/2 left-1/2 w-1 h-1 bg-white/50 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
    </div>
    <div class="flex flex-col items-center p-4 relative bg-white">
      <image class="size-20 mt-2 mb-4 rounded-lg" mode="widthFix" :src="monkey.$url.cdn(monkey.$config.about.officialAvatar)"></image>
      <view class="text-lg font-bold mb-2">登录</view>
      <view class="text-sm mb-6 text-gray-600">欢迎您登录【{{ monkey.$config.about.appName }}】</view>
      <view class="flex flex-row items-center mb-4 w-full">
        <checkbox style="transform: scale(0.6)" :checked="isAgree" @tap="toggleAgree" />
        <text class="text-sm text-gray-700">我已阅读并遵守</text>
        <text class="text-sm text-blue-500 font-medium" @tap="openAgreement">《用户协议》</text>
      </view>

      <view class="cu-btn-style !w-[620rpx] mb-3 relative overflow-hidden" :class="!isAgree ? 'opacity-50' : ''" @click.stop="handleViewTap">
        <button :disabled="!isAgree" class="absolute inset-0 w-full h-full opacity-0" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" />
        <view class="flex items-center justify-center h-full pointer-events-none">
          <text class="iconfont icon-phone mr-2"></text>
          手机号快捷登录
        </view>
      </view>
      <div class="!w-[620rpx] text-center text-sm text-gray-500 underline" @tap="handleCancelLogin">暂不登录</div>
      <view class="mt-4 text-sm flex items-center pb-[calc(env(safe-area-inset-bottom)+10px)]">
        <text class="text-gray-500">登录遇到问题? </text>
        <button open-type="contact" class="text-blue-500 font-medium flex items-center border-none bg-transparent p-0 m-0 text-sm after:border-none">
          <text class="iconfont icon-service mr-1"></text>
          点此联系客服
        </button>
      </view>
    </div>
  </van-popup>

  <!-- 用户协议弹窗 -->
  <van-popup ref="agreementPopup" round v-model:show="agreementVisible" position="center" @click-overlay="agreementVisible = false">
    <view class="bg-white rounded-2xl w-690 overflow-hidden shadow-2xl">
      <!-- 协议标题栏 -->
      <view class="flex items-center justify-between p-4 border-b border-gray-100 bg-blue-50">
        <view class="text-lg font-bold text-gray-800">用户协议</view>
        <view class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center transition-colors duration-200 active:bg-gray-200" @tap="closeAgreement">
          <i class="i-mdi-close text-gray-600 text-lg"></i>
        </view>
      </view>

      <!-- 协议内容 -->
      <scroll-view scroll-y class="max-h-[60vh] w-full box-border">
        <view class="text-sm text-gray-700 p-4 leading-relaxed space-y-4">
          <view class="font-medium text-gray-800 mb-3">欢迎使用{{ monkey.$config.about.appName }}</view>

          <view class="space-y-3">
            <view>
              <text class="font-medium text-blue-600">1. 服务条款</text>
              <text class="block mt-1 text-gray-600">本协议是您与{{ monkey.$config.about.appName }}之间关于使用本服务的法律协议。使用本服务即表示您同意遵守本协议的所有条款。</text>
            </view>

            <view>
              <text class="font-medium text-blue-600">2. 用户账户</text>
              <text class="block mt-1 text-gray-600">您需要创建账户才能使用某些服务功能。您有责任保护账户信息的安全，并对账户下的所有活动负责。</text>
            </view>

            <view>
              <text class="font-medium text-blue-600">3. 隐私保护</text>
              <text class="block mt-1 text-gray-600">我们重视您的隐私，会按照隐私政策收集、使用和保护您的个人信息。您的手机号仅用于账户验证和安全保护。</text>
            </view>

            <view>
              <text class="font-medium text-blue-600">4. 服务使用</text>
              <text class="block mt-1 text-gray-600">您同意合法使用本服务，不得进行任何违法、有害或干扰服务正常运行的行为。</text>
            </view>

            <view>
              <text class="font-medium text-blue-600">5. 免责声明</text>
              <text class="block mt-1 text-gray-600">本服务按"现状"提供，我们不对服务的连续性、准确性或完整性做出保证。</text>
            </view>

            <view>
              <text class="font-medium text-blue-600">6. 协议变更</text>
              <text class="block mt-1 text-gray-600">我们可能会不时更新本协议。重大变更会通过适当方式通知您。继续使用服务即表示接受更新后的协议。</text>
            </view>
          </view>

          <view class="text-xs text-gray-400 mt-6 pt-4 border-t border-gray-100"> 最后更新时间：{{ new Date().toLocaleDateString() }} </view>
        </view>
      </scroll-view>

      <!-- 协议底部按钮 -->
      <view class="p-4 border-t border-gray-100 bg-gray-50">
        <view class="flex gap-3 text-xs">
          <view class="flex-1 h-10 flex items-center justify-center bg-gray-200 text-gray-600 rounded-lg transition-colors duration-200 tracking-wide active:bg-gray-300" @tap="closeAgreement">取消</view>
          <view class="flex-1 h-10 flex items-center justify-center bg-blue-500 text-white rounded-lg transition-colors duration-200 tracking-wide active:bg-blue-600" @tap="agreeAndClose">同意并继续</view>
        </view>
      </view>
    </view>
  </van-popup>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  // 获取当前实例
  const instance: ComponentInternalInstance = getCurrentInstance();

  // 获取登录弹窗状态
  const authModalStore = monkey.$stores.useAuthModalStore();

  // 获取登录弹窗状态
  const { visible } = storeToRefs(authModalStore);

  const { setToken, setUser, setSessionKey, setAuthineToken } = monkey.$stores.useUserStore();

  // 协议同意状态
  const isAgree = ref(false);

  // 协议弹窗状态
  const agreementVisible = ref(false);

  // 切换协议同意状态
  const toggleAgree = () => {
    isAgree.value = !isAgree.value;
  };

  // 打开用户协议
  const openAgreement = () => {
    agreementVisible.value = true;
  };

  // 关闭用户协议弹窗
  const closeAgreement = () => {
    agreementVisible.value = false;
  };

  // 点击遮罩层
  const handleMaskClick = () => {
    authModalStore.close();
  };

  // 同意协议并关闭弹窗
  const agreeAndClose = () => {
    isAgree.value = true;
    closeAgreement();
    monkey.$helper.toast.success('已同意用户协议');
  };

  /**
   * 获取手机号
   * @param e 事件
   * @returns void
   */
  const getPhoneNumber = (e: any) => {
    // 如果用户不同意协议，则提示用户
    if (!isAgree.value) return monkey.$helper.toast.error('请先同意用户协议和隐私政策');

    // 如果用户同意协议，则获取手机号
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 获取到手机号，进行登录
      const encryptedData = e.detail.encryptedData;
      const iv = e.detail.iv;
      console.log('getPhoneNumber:', e.detail.code);
      monkey.$helper.toast.loading('登录中...');
      // 调用登录接口
      uni.login({
        provider: 'weixin',
        success: async (loginRes) => {
          if (loginRes.code) {
            // 请求服务器进行登录验证
            console.log('微信登录成功，code:', loginRes.code);
            console.log('加密数据:', encryptedData);
            console.log('IV:', iv);
            // 请求服务器进行登录验证
            try {
              const userRes = await monkey.$api.user.getWxLoginCode(loginRes.code);

              if (userRes.errcode == 200 && userRes.data) {
                const { token, user } = userRes.data;
                setToken(token);
                setSessionKey(userRes.msg);
                // 获取手机号码
                const phoneRes = await monkey.$api.user.getWxPhone(e.detail.code);
                if (phoneRes.errcode == 200 && phoneRes.data) {
                  setUser(phoneRes.data);
                  setToken(phoneRes.data.token);
                  setAuthineToken(phoneRes.data.authorization);
                  monkey.$helper.toast.success('登录成功');
                  authModalStore.close(false);
                }
              }
            } catch (error) {
              console.log('登录失败:', error);
            } finally {
              monkey.$helper.toast.hideLoading();
            }
          } else {
            monkey.$helper.toast.error('微信登录失败');
          }
        },
        fail: () => {
          monkey.$helper.toast.error('微信登录失败');
        },
      });
    } else {
      monkey.$helper.toast.error('您已拒绝授权获取手机号');
    }
  };

  // 处理视图点击，防止穿透
  const handleViewTap = (e: any) => {
    // e.stopPropagation();
    // console.log('handleViewTap:', e);
    // // 如果未同意协议，提示用户
    // if (!isAgree.value) {
    //   monkey.$helper.toast.error('请先同意用户协议');
    //   return;
    // }
    // 如果同意了协议，这里不需要额外处理，button 的 getPhoneNumber 会自动触发
  };

  // 取消登录
  const handleCancelLogin = () => {
    authModalStore.close();
  };

  /**
   * 监听登录弹窗可见状态
   * 如果登录弹窗可见，则打开登录弹窗
   * 如果登录弹窗不可见，则关闭登录弹窗
   */
  watch(
    visible,
    (newVal: boolean) => {
      if (newVal) {
        isAgree.value = false;
      }
    },
    { immediate: true, deep: true },
  );

</script>
<style lang="scss" scoped></style>
