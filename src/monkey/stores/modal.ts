import type { ModalState } from './types';

/**
 * 认证模态框状态管理
 */
export const useAuthModalStore = defineStore('authModal', () => {
  const state = reactive<ModalState>({
    visible: false,
  });

  /**
   * 打开登录弹窗
   */
  function open(): void {
    state.visible = true;
  }

  /**
   * 关闭登录弹窗
   */
  function close(): void {
    state.visible = false;
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    // 如果前面有的话 那就回退
    if (pages.length > 1) {
      uni.navigateBack();
    }
  }

  return {
    ...toRefs(state),
    open,
    close,
  };
});
