// 路由
interface Router {
    navigateTo(path: string, options: RouterOptions): RouterResult
    redirectTo(path: string, options: RouterOptions): RouterResult
    switchTab(path: string, options: RouterOptions): RouterResult
    reLaunch(path: string, options: RouterOptions): RouterResult
    navigateBack(delta: number, options: RouterOptions): RouterResult
}

// 路由选项
interface RouterOptions {
    delay?: number
}

interface RouterResult {
    success: boolean
    message: string
}

const router: Router = {
    /**
     * 跳转页面
     * @param path 页面路径
     * @param delay 延迟时间
     */
    navigateTo(path: string, options: RouterOptions = {}) {
        setTimeout(() => {
            uni.navigateTo({
                url: path
            })
        }, options.delay || 0)
    },

    /**
     * 重定向页面
     * @param path 页面路径
     * @param delay 延迟时间
     */
    redirectTo(path: string, options: RouterOptions = {}) {
        setTimeout(() => {
            uni.redirectTo({
                url: path
            })
        }, options.delay || 0)
    },

    /**
     * 切换tab
     * @param path 页面路径
     * @param delay 延迟时间
     */
    switchTab(path: string, options: RouterOptions = {}) {
        setTimeout(() => {
            uni.switchTab({
                url: path
            })
        }, options.delay || 0)
    },

    /**
     * 重新启动
     * @param path 页面路径
     * @param delay 延迟时间
     */
    reLaunch(path: string, options: RouterOptions = {}) {
        setTimeout(() => {
            uni.reLaunch({
                url: path
            })
        }, options.delay || 0)
    },

    /**
     * 返回
     * @param delta 返回层级
     * @param delay 延迟时间
     */
    navigateBack(delta: number = 1, options: RouterOptions = {}) {
        setTimeout(() => {
            uni.navigateBack({
                delta
            })
        }, options.delay || 0)
    }
}
export default router;