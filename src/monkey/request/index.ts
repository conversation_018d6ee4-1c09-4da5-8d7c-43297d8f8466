import monkey from '@/monkey';
import { CommonTypes } from '../types';

/**
 * 发起网络请求的封装函数。
 * @param {Object} options - 请求配置选项对象。
 * @returns {Promise} 返回一个Promise对象，成功时resolve携带响应数据，失败时reject携带错误信息。
 */
const request = async (options: CommonTypes.RequestOptions) => {
  const defaultToken = true;

  const defaultLoading = {
    show: true,
    title: '加载中...',
  };

  // 如果传入了loading配置，则合并默认值和传入值
  if (options.loading) {
    options.loading = {
      ...defaultLoading,
      ...options.loading,
    };
  } else {
    options.loading = defaultLoading;
  }

  // 如果传入了token配置，则合并默认值和传入值
  options.token = options.token === 'no' ? false : true;

  console.log("🚀 ~ request ~   options.token:", options.token)

  console.log('🚀 ~ request ~ options.source:', options.source);
  // 处理认证相关的请求
  if (options.source) {
    const userStore = monkey.$stores.useUserStore();
    try {
      console.log('🚀 ~ request ~ userStore.authineTokenExpiresIn:', userStore.authineTokenExpiresIn);
      // 检查authineToken是否存在且是否过期
      if (userStore.authineTokenExpiresIn && Date.now() > userStore.authineTokenExpiresIn) {
        // Token已过期，刷新token
        console.log('AuthineToken已过期，正在刷新...');
        await userStore.getAuthineToken();
      } else if (!userStore.authineTokenExpiresIn) {
        console.log('AuthineToken未过期，直接请求');
        await userStore.getAuthineToken();
      }
    } catch (error) {
      console.error('处理认证Token时出错:', error);
    }
  }
  // 创建并返回一个新的Promise对象，处理异步请求
  return new Promise((resolve, reject) => {
    // 使用uni.request发起网络请求
    uni.request({
      ...options, // 使用扩展运算符合并传入的请求配置选项
      timeout: 6000000, // 设置请求超时时间为600秒
      success: (res) => {
        resolve(res.data);
      }, // 请求成功时，将响应数据resolve
      fail: (err) => {
        reject(err);
      }, // 请求失败时，将错误信息reject
    });
  });
};

export default request;
