import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');
import lodash from 'lodash';
import config from '../tthz-config';
import router from './router';
import helper from './helper';
import stores from './stores';
import api from './api';
import url from './url';

const monkey = {
  $config: config,
  $router: router,
  $helper: helper,
  $stores: stores,
  $api: api,
  $dayjs: dayjs,
  $url: url,
  $lodash: lodash,
};

export default monkey;