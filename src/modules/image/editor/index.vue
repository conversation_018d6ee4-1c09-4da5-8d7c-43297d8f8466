<template>
  <ly-layout>
    <!-- 表单内容区域 -->
    <view class="p-4">
      <uni-forms ref="formRef" :model="formData" :rules="rules" label-position="top" :border="false">
        <!-- 内容编辑卡片 -->
        <view class="bg-white rounded-xl shadow-sm p-4 mb-4">
          <uni-forms-item name="content" label="内容编辑" required>
            <uni-easyinput v-model="formData.content" type="textarea" placeholder="分享你的想法..." :maxlength="500" :auto-height="true" :min-height="120" :input-border="false" class="content-textarea" />
            <view class="flex justify-between items-center mt-2">
              <text class="text-xs text-gray-400">请合理发布社区内容</text>
              <text class="text-xs text-gray-400">{{ formData.content.length }}/500</text>
            </view>
          </uni-forms-item>
        </view>

        <!-- 图片上传卡片 -->
        <view class="bg-white rounded-xl shadow-sm p-4 mb-4">
          <uni-forms-item name="images" label="添加图片" required>
            <uni-file-picker v-model="formData.images" title="最多上传9张图片" :max-count="maxImages" :max-size="10 * 1024 * 1024" :size-type="['compressed']" :source-type="['camera', 'album']">
              <div class="flex items-center justify-center flex-col gap-2">
                <text class="i-mdi-camera-enhance-outline text-2xl text-theme-blue"></text>
                <text class="text-sm text-gray-400">上传图片</text>
              </div>
            </uni-file-picker>
          </uni-forms-item>
        </view>
      </uni-forms>
    </view>

    <!-- 底部固定按钮 -->
    <ly-fixed-btns :buttons="buttons" />
  </ly-layout>
</template>

<script setup lang="ts">
import monkey from '@/monkey';
import type { ImagePost, UploadResult } from '@/monkey/types/image';
import type { FormRules } from '@/monkey/types/common';
import { round } from 'lodash';

// 表单引用
const formRef = ref();

// 最大图片数量
const maxImages = 9;

// 表单数据
const formData = reactive({
  content: '',
  images: [] as UploadResult[],
});

const buttons = [
  {
    text: '发布社区',
    icon: 'i-mdi-send',
    round: 'rounded-full',
    click: publishPost,
  },
];

// 表单验证规则
const rules: FormRules = {
  content: {
    rules: [
      { required: true, errorMessage: '请输入内容' },
      { minLength: 5, errorMessage: '内容至少5个字符' },
      { maxLength: 500, errorMessage: '内容不能超过500个字符' },
    ],
  },
};

// 计算是否可以发布
const canPublish = computed(() => {
  return formData.content.trim().length >= 5 && (formData.content.length > 0 || formData.images.length > 0);
});

// 保存草稿
const saveDraft = () => {
  if (!formData.content.trim() && formData.images.length === 0) {
    monkey.$helper.toast.error('请先添加内容或图片');
    return;
  }

  // 保存到本地存储
  uni.setStorageSync('image_editor_draft', formData);
  monkey.$helper.toast.success('草稿已保存');
};

// 发布社区
const publishPost = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    monkey.$helper.toast.loading('发布中...');

    // 构造发布数据
    const postData: Partial<ImagePost> = {
      content: formData.content,
      images: formData.images,
      createTime: new Date().toISOString(),
    };

    // 这里应该调用发布接口
    // const result = await monkey.$api.image.createPost(postData);

    // 模拟发布成功
    setTimeout(() => {
      monkey.$helper.toast.hideLoading();
      monkey.$helper.toast.success('发布成功');

      // 清除草稿
      uni.removeStorageSync('image_editor_draft');

      // 返回上一页或跳转到社区列表
      uni.navigateBack();
    }, 2000);
  } catch (error) {
    monkey.$helper.toast.hideLoading();
    console.error('表单验证失败:', error);
  }
};

// 加载草稿
const loadDraft = () => {
  try {
    const draft = uni.getStorageSync('image_editor_draft');
    if (draft) {
      Object.assign(formData, draft);
    }
  } catch (error) {
    console.error('加载草稿失败:', error);
  }
};

// 页面加载时恢复草稿
onMounted(() => {
  loadDraft();
});
</script>

<style scoped></style>
