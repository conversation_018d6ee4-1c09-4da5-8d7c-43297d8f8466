<template>
  <ly-layout>
    <uni-nav-bar left-icon="left" leftWidth="200rpx" :background-color="navBarBgColor" :color="navBarColor" leftText="商品详情" :fixed="true" statusBar :border="false" />
    <!-- 内容区域 -->
    <scroll-view scroll-y class="flex-1">
      <!-- 轮播图 -->
      <swiper class="w-full h-750 aspect-square" indicator-dots autoplay circular :interval="3000" :duration="500" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#006dbb">
        <swiper-item v-for="(image, index) in goodsDetail.images" :key="index">
          <image :src="image" mode="aspectFill" class="w-full h-full" />
        </swiper-item>
      </swiper>

      <!-- 价格信息 -->
      <div class="bg-white p-4">
        <div class="flex items-center space-x-2 mb-2">
          <div class="bg-red-600/10 px-2 py-1 rounded text-xs font-semibold text-red-600">
            {{ goodsDetail.discount }}
          </div>
          <div v-for="(tag, index) in goodsDetail.tags" :key="index" class="px-2 py-1 rounded text-xs font-semibold text-blue-600 bg-blue-50">
            {{ tag }}
          </div>
        </div>

        <div class="flex items-baseline space-x-3 mb-2">
          <div class="flex items-baseline">
            <text class="text-2xl font-bold text-red-600">{{ goodsDetail.price }}</text>
            <text class="text-sm text-gray-500 ml-1">{{ goodsDetail.unit }}</text>
          </div>
          <text class="text-sm text-gray-400 line-through">{{ goodsDetail.originalPrice }}</text>
        </div>

        <div class="text-lg font-medium text-gray-800 mb-2 leading-tight">
          {{ goodsDetail.name }}
        </div>

        <div class="flex items-center justify-between text-xs text-gray-500">
          <div>已售 {{ goodsDetail.sales.toLocaleString() }}</div>
          <div>{{ goodsDetail.location }}</div>
        </div>
      </div>

      <!-- 服务保障 -->
      <div class="bg-white mt-2 p-4">
        <div class="flex items-center mb-4">
          <text class="i-mdi-shield-check text-blue-500 text-xl mr-2"></text>
          <div class="text-base font-medium text-gray-800">服务保障</div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <text class="i-mdi-check-circle text-green-500 mr-1"></text>
            <text class="text-xs text-gray-600">官方认证</text>
          </div>
          <div class="flex items-center">
            <text class="i-mdi-truck-fast text-green-500 mr-1"></text>
            <text class="text-xs text-gray-600">极速发货</text>
          </div>
          <div class="flex items-center">
            <text class="i-mdi-shield-check text-green-500 mr-1"></text>
            <text class="text-xs text-gray-600">品质保障</text>
          </div>
        </div>
      </div>

      <!-- 质量标准 -->
      <div class="bg-white mt-2 p-4">
        <div class="flex items-center mb-4">
          <text class="i-mdi-certificate text-green-500 text-xl mr-2"></text>
          <div class="text-base font-medium text-gray-800">质量标准</div>
        </div>
        <div class="bg-green-50 rounded-lg p-3">
          <div class="flex items-center mb-3">
            <text class="i-mdi-certificate text-green-600 mr-2 text-lg"></text>
            <text class="text-sm font-medium text-green-700">国家农产品质量安全标准</text>
          </div>
          <div class="text-xs text-gray-600 leading-relaxed mb-2">本产品严格按照GB 2762-2017《食品安全国家标准 食品中污染物限量》生产</div>
          <div class="flex items-center justify-between mt-1">
            <div class="flex items-center">
              <text class="i-mdi-check-decagram text-green-600 mr-1 text-base"></text>
              <text class="text-xs text-gray-700">绿色食品认证</text>
            </div>
            <div class="flex items-center">
              <text class="i-mdi-check-decagram text-green-600 mr-1 text-base"></text>
              <text class="text-xs text-gray-700">无农药残留</text>
            </div>
            <div class="flex items-center">
              <text class="i-mdi-check-decagram text-green-600 mr-1 text-base"></text>
              <text class="text-xs text-gray-700">有机认证</text>
            </div>
          </div>
        </div>
      </div>

      <!-- 店铺信息 -->
      <div class="bg-white mt-2 p-4">
        <div class="flex items-center mb-4">
          <text class="i-mdi-store text-amber-500 text-xl mr-2"></text>
          <div class="text-base font-medium text-gray-800">店铺信息</div>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
              <div class="text-sm font-bold text-gray-600">店</div>
            </div>
            <div>
              <div class="text-sm font-medium">{{ goodsDetail.shop }}</div>
              <div class="text-xs text-gray-500">{{ goodsDetail.location }}</div>
            </div>
          </div>
          <div class="px-3 py-1.5 rounded-full border border-blue-500 text-blue-500 text-xs">进店逛逛</div>
        </div>
      </div>

      <!-- 联系方式 -->
      <div class="bg-white mt-2 p-4">
        <div class="flex items-center mb-4">
          <text class="i-mdi-phone text-purple-500 text-xl mr-2"></text>
          <div class="text-base font-medium text-gray-800">联系方式</div>
        </div>
        <div class="flex items-center space-x-3">
          <div class="flex items-center p-3 rounded-lg bg-blue-50 flex-1">
            <text class="i-mdi-phone text-blue-600 text-lg mr-2"></text>
            <div>
              <div class="text-xs text-gray-500">客服电话</div>
              <div class="text-sm font-medium text-gray-800">************</div>
            </div>
          </div>
          <div class="flex items-center p-3 rounded-lg bg-blue-50 flex-1">
            <text class="i-mdi-store text-blue-600 text-lg mr-2"></text>
            <div>
              <div class="text-xs text-gray-500">店铺名称</div>
              <div class="text-sm font-medium text-gray-800">{{ goodsDetail.shop }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品详情 -->
      <div class="bg-white mt-2 p-4">
        <div class="flex items-center mb-4">
          <text class="i-mdi-text-box text-red-500 text-xl mr-2"></text>
          <div class="text-base font-medium text-gray-800">商品详情</div>
        </div>
        <div>
          <div class="text-sm text-gray-600 leading-relaxed">
            <p class="mb-3">{{ goodsDetail.description }}</p>
            <div v-for="(detail, index) in goodsDetail.details" :key="index" class="mb-3">
              <div class="font-medium mb-1 text-gray-700">{{ detail.title }}</div>
              <div class="text-gray-600">{{ detail.content }}</div>
            </div>
          </div>
          <div class="mt-4">
            <image v-for="(img, index) in goodsDetail.detailImages" :key="index" :src="img" mode="widthFix" class="w-full mb-2" />
          </div>
        </div>
      </div>

      <!-- 相似推荐 -->
      <div class="bg-white mt-2 p-4 mb-20">
        <div class="text-sm font-medium mb-3">相似推荐</div>
        <div class="grid grid-cols-2 gap-3">
          <div v-for="(item, index) in recommendList" :key="index" class="bg-gray-50 rounded-lg overflow-hidden">
            <image :src="item.image" mode="aspectFill" class="w-full aspect-square" />
            <div class="p-2">
              <div class="text-xs text-gray-800 line-clamp-2 mb-1">{{ item.name }}</div>
              <div class="text-sm text-red-600 font-bold">{{ item.price }}</div>
            </div>
          </div>
        </div>
      </div>
    </scroll-view>

    <!-- 底部操作栏 -->
    <ly-fixed-btns type="goods-detail">
      <div class="w-full bg-white text-gray-800 flex items-center tracking-widest justify-center">
        <div class="flex items-center space-x-4 mr-3">
          <div class="flex flex-col items-center">
            <text class="i-mdi-store text-xl text-gray-600"></text>
            <text class="text-xs text-gray-500">店铺</text>
          </div>
          <div class="flex flex-col items-center">
            <text class="i-mdi-heart-outline text-xl text-gray-600"></text>
            <text class="text-xs text-gray-500">收藏</text>
          </div>
          <div class="flex flex-col items-center">
            <text class="i-mdi-message-text-outline text-xl text-gray-600"></text>
            <text class="text-xs text-gray-500">客服</text>
          </div>
        </div>
        <div class="flex-1 flex space-x-2">
          <div class="flex-1 h-10 bg-orange-500 rounded-full flex items-center justify-center" @click="handleAddCart">
            <text class="text-sm text-white font-medium">加入购物车</text>
          </div>
          <div class="flex-1 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center" @click="handleBuyNow">
            <text class="text-sm text-white font-medium">立即购买</text>
          </div>
        </div>
      </div>
    </ly-fixed-btns>
  </ly-layout>
</template>
<script setup lang="ts">
  import monkey from '@/monkey';

  // 获取页面参数
  const goodsId = ref('');

  const navBarBgColor: Ref<string> = ref('rgba(255, 255, 255, 0)');

  const navBarColor: Ref<string> = ref('rgba(0, 0, 0, 0)');

  onPageScroll((e: Page.PageScrollOption) => {
    const scrollTop = e.scrollTop;
    // 设置滚动阈值，这里设为200，您可以根据需要调整
    const threshold = 200;

    if (scrollTop > threshold) {
      // 超过阈值时设置不透明背景
      navBarBgColor.value = `rgba(255, 255, 255, ${scrollTop / threshold})`;
      navBarColor.value = `rgba(0, 0, 0, ${scrollTop / threshold})`;
    } else {
      navBarBgColor.value = `rgba(255, 255, 255, 0)`;
      navBarColor.value = `rgba(0, 0, 0, ${scrollTop / threshold})`;
    }
  });

  // 获取URL参数
  onLoad((options: any) => {
    if (options && options.id) {
      goodsId.value = options.id;
      // 实际项目中这里应该调用API获取商品数据
      fetchGoodsDetail(goodsId.value);
    }
  });

  // 获取商品详情
  const fetchGoodsDetail = (id: string) => {
    // 实际项目中这里应该是API调用
    console.log('Fetching goods detail for ID:', id);
    // 这里使用模拟数据
  };

  // 返回上一页
  const handleBack = () => {
    monkey.$router.navigateBack();
  };

  // 加入购物车
  const handleAddCart = () => {
    uni.showToast({
      title: '已加入购物车',
      icon: 'success',
    });
  };

  // 立即购买
  const handleBuyNow = () => {
    uni.navigateTo({
      url: '/modules/order/create/index?goodsId=' + goodsId.value,
    });
  };

  // 模拟数据 - 商品详情
  const goodsDetail = ref({
    id: '1',
    name: '优质花生米 精选大粒 农家自产 无添加',
    originalPrice: '¥15.80',
    price: '¥12.50',
    unit: '/kg',
    sales: 2580,
    location: '山东济南',
    images: ['https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500', 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500', 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'],
    discount: '8折',
    tags: ['现货', '包邮'],
    shop: '济南农产品专营店',
    description: '本店花生米选用优质品种，颗粒饱满，口感香脆，营养丰富，无任何添加剂，纯天然种植，健康美味。',
    details: [
      { title: '产地', content: '山东济南' },
      { title: '保质期', content: '12个月' },
      { title: '储存方式', content: '避光、阴凉、干燥处' },
      { title: '净含量', content: '500g/袋' },
    ],
    detailImages: ['https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500', 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'],
  });

  // 推荐商品列表
  const recommendList = [
    {
      id: '2',
      name: '东北大米 五常稻花香',
      price: '¥8.80',
      image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    },
    {
      id: '3',
      name: '新疆棉花 长绒棉',
      price: '¥15.20',
      image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    },
    {
      id: '4',
      name: '优质玉米 饲料级',
      price: '¥3.60',
      image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    },
    {
      id: '5',
      name: '有机大豆 非转基因',
      price: '¥7.80',
      image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    },
  ];
</script>

<style scoped lang="scss">
  :deep(.uni-nvue-fixed) {
    @apply fixed top-0 left-0 right-0 z-50;
  }

  :deep(.uniui-left) {
    color: #333 !important;
  }

  :deep(.uni-navbar-btn-text text) {
    font-size: 32rpx !important;
    font-weight: bolder !important;
    margin-left: 12rpx !important;
  }
</style>
