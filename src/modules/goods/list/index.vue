<template>
  <ly-layout>
    <div class="flex h-screen relative overflow-hidden">
      <!-- 左侧分类菜单 -->
      <scroll-div class="w-[140rpx] bg-gray-50 h-full" scroll-y>
        <div v-for="(item, index) in categoryList" :key="index" class="h-12 flex items-center justify-center text-sm text-gray-700 relative" :class="{ 'bg-white text-[#006dbb] font-bold': currentCategory === index }" @tap="changeCategory(index)">
          <di v v-if="currentCategory === index" class="absolute left-0 top-[30rpx] h-[40rpx] w-[8rpx] bg-blue-500 rounded-r"></di>
          {{ item.name }}
        </div>
      </scroll-div>

      <!-- 右侧商品列表 -->
      <div class="h-full flex-1 bg-white relative">
        <div class="py-3 px-3 border-b border-gray-100">
          <text class="block text-base font-bold mb-2">{{ currentCategoryName }}</text>
          <div class="flex justify-between items-center">
            <div class="flex items-center">
              <text class="mr-4 text-sm relative" :class="sortType === 'default' ? 'text-blue-500' : 'text-gray-500'" @tap="changeSort('default')">默认</text>
              <text class="mr-4 text-sm relative" :class="sortType === 'sales' ? 'text-blue-500' : 'text-gray-500'" @tap="changeSort('sales')">销量</text>
              <text class="mr-4 text-sm relative" :class="sortType === 'price' ? 'text-blue-500' : 'text-gray-500'" @tap="changeSort('price')">
                价格
                <div class="inline-block ml-1 relative">
                  <div v-if="sortType === 'price' && sortOrder === 'asc'" class="absolute left-0 top-[-12rpx] border-[10rpx] border-transparent border-b-pink-500"></div>
                  <div v-if="sortType === 'price' && sortOrder === 'desc'" class="absolute left-0 top-[2rpx] border-[10rpx] border-transparent border-t-pink-500"></div>
                </div>
              </text>
            </div>
            <div class="flex items-center">
              <text class="i-mdi-view-grid-outline text-base text-gray-500" v-if="!isHorizontal" @tap="isHorizontal = true"></text>
              <text class="i-mdi-format-list-text text-base text-gray-500" v-else @tap="isHorizontal = false"></text>
            </div>
          </div> 
        </div>
        <scroll-view class="absolute top-[170rpx] w-full bottom-0" scroll-y @scrolltolower="loadMore">
          <div class="px-2.5">
            <ly-goods-list :is-horizontal="isHorizontal" />
          </div>
        </scroll-view>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { ref, computed, onMounted } from 'vue';

  const navBarBgColor = ref('transparent');
  const navBarTitleColor = ref('#fff');
  const isHorizontal = ref(true);

  // 分类列表
  const categoryList = ref([
    { id: 1, name: '推荐' },
    { id: 2, name: '手机' },
    { id: 3, name: '家用' },
    { id: 4, name: '电脑' },
    { id: 5, name: '食品' },
    { id: 6, name: '服装' },
    { id: 7, name: '美妆' },
    { id: 8, name: '家居' },
  ]);

  // 当前选中的分类
  const currentCategory = ref(0);
  const currentCategoryName = computed(() => categoryList.value[currentCategory.value].name);

  // 商品列表
  const goodsList = ref([]);
  // 排序方式
  const sortType = ref('default');
  const sortOrder = ref('desc');
  // 加载状态
  const loadingStatus = ref('more');
  // 页码
  const page = ref(1);
  const pageSize = ref(10);

  // 切换分类
  const changeCategory = (index: number) => {
    if (currentCategory.value === index) return;
    currentCategory.value = index;
    resetList();
    getGoodsList();
  };

  // 切换排序
  const changeSort = (type: string) => {
    if (sortType.value === type && type === 'price') {
      // 价格排序切换升降序
      sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
    } else {
      sortType.value = type;
      sortOrder.value = 'desc';
    }
    resetList();
    getGoodsList();
  };

  // 重置列表
  const resetList = () => {
    page.value = 1;
    goodsList.value = [];
    loadingStatus.value = 'more';
  };

  // 获取商品列表
  const getGoodsList = () => {
    if (loadingStatus.value === 'noMore') return;
    loadingStatus.value = 'loading';

    // 模拟请求数据
    setTimeout(() => {
      const mockData = Array(10)
        .fill(0)
        .map((_, idx) => ({
          id: page.value * 100 + idx,
          name: `${currentCategoryName.value}商品${page.value}-${idx + 1}`,
          image: 'https://picsum.photos/200/300',
          price: Math.floor(Math.random() * 1000 + 100),
          sales: Math.floor(Math.random() * 1000),
        }));

      goodsList.value = [...goodsList.value, ...mockData];
      loadingStatus.value = page.value >= 3 ? 'noMore' : 'more';
      page.value++;
    }, 500);
  };

  // 加载更多
  const loadMore = () => {
    if (loadingStatus.value !== 'more') return;
    getGoodsList();
  };

  // 进入商品详情
  const goGoodsDetail = (id: number) => {
    uni.navigateTo({
      url: `/modules/goods/detail/index?id=${id}`,
    });
  };

  onMounted(() => {
    getGoodsList();
  });

  onPageScroll((e) => {
    navBarBgColor.value = monkey.$helper.utilss.getNavBarBgColor(e.scrollTop);
    navBarTitleColor.value = monkey.$helper.utilss.getNavTitleColor(e.scrollTop);
  });
</script>
