<template>
  <ly-layout>
    <!-- 药材价格信息卡片 -->
    <div v-if="currentDetail" class="mx-4 my-3">
      <div class="relative bg-gradient-to-br from-theme-blue via-theme-blue to-theme-blue rounded-xl shadow-lg border border-blue-400/20 overflow-hidden">
        <!-- 装饰性背景元素 -->
        <div class="absolute top-0 right-0 w-20 h-20 bg-white/5 rounded-full -translate-y-10 translate-x-10"></div>

        <!-- 卡片头部 -->
        <div class="relative p-4 pb-0">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center gap-2">
              <div class="w-8 h-8 bg-white/15 rounded-lg flex items-center justify-center">
                <text class="i-mdi-leaf text-white text-sm"></text>
              </div>
              <div>
                <div class="text-lg font-bold text-white">{{ currentDetail.ycmc }}</div>
              </div>
            </div>
            <div class="bg-white/15 px-2 py-1 rounded-md">
              <text class="text-xs text-white/90">{{ monkey.$dayjs(currentDetail.jgtjrq).format('YYYY-MM-DD') }}</text>
            </div>
          </div>
        </div>

        <!-- 当前价格区域 -->
        <div class="relative px-4 py-2">
          <div class="bg-white/10 rounded-lg p-3 border border-white/20">
            <div class="text-center">
              <div class="text-xs text-white/70 mb-1">当前价格</div>
              <div class="text-2xl font-bold text-white">
                {{ detail.jg ? Number(detail.jg).toFixed(2) : '0.00' }}
                <text class="text-sm text-white/80 font-normal ml-1">元</text>
              </div>
            </div>
          </div>
        </div>

        <!-- 价格统计信息 -->
        <div class="relative px-4 pb-4">
          <div class="grid grid-cols-3 gap-2">
            <div class="bg-white/10 rounded-lg p-2 text-center border border-white/10">
              <div class="w-6 h-6 bg-green-400/20 rounded-md flex items-center justify-center mx-auto mb-1">
                <text class="i-mdi-trending-down text-green-300 text-xs"></text>
              </div>
              <div class="text-white text-xs mb-0.5">最低</div>
              <div class="text-white font-bold text-xs">{{ priceStats.minPrice.toFixed(2) }}元</div>
            </div>
            <div class="bg-white/10 rounded-lg p-2 text-center border border-white/10">
              <div class="w-6 h-6 bg-red-400/20 rounded-md flex items-center justify-center mx-auto mb-1">
                <text class="i-mdi-trending-up text-red-300 text-xs"></text>
              </div>
              <div class="text-white text-xs mb-0.5">最高</div>
              <div class="text-white font-bold text-xs">{{ priceStats.maxPrice.toFixed(2) }}元</div>
            </div>
            <div class="bg-white/10 rounded-lg p-2 text-center border border-white/10">
              <div class="w-6 h-6 bg-yellow-400/20 rounded-md flex items-center justify-center mx-auto mb-1">
                <text class="i-mdi-chart-line text-yellow-300 text-xs"></text>
              </div>
              <div class="text-white text-xs mb-0.5">平均</div>
              <div class="text-white font-bold text-xs">{{ priceStats.avgPrice.toFixed(2) }}元</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="mx-4 my-3">
      <div class="relative bg-gradient-to-br from-gray-100 via-gray-200 to-gray-300 rounded-xl shadow-lg border border-gray-200/50 overflow-hidden">
        <!-- 装饰性背景元素 -->
        <div class="absolute top-0 right-0 w-20 h-20 bg-white/30 rounded-full -translate-y-10 translate-x-10"></div>

        <!-- 卡片头部 -->
        <div class="relative p-4 pb-2">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center gap-2">
              <div class="w-8 h-8 bg-gray-300/50 rounded-lg flex items-center justify-center">
                <text class="i-mdi-leaf text-gray-500 text-sm"></text>
              </div>
              <div>
                <div class="text-lg font-bold text-gray-700">药材价格</div>
              </div>
            </div>
            <div class="bg-white/60 px-2 py-1 rounded-md">
              <text class="text-xs text-gray-600">无数据</text>
            </div>
          </div>
        </div>

        <!-- 当前价格区域 -->
        <div class="relative px-4 py-2">
          <div class="bg-white/50 rounded-lg p-3 border border-gray-300/30">
            <div class="text-center">
              <div class="text-xs text-gray-500 mb-1">当前价格</div>
              <div class="text-2xl font-bold text-gray-400">
                --
                <text class="text-sm text-gray-400 font-normal ml-1">元</text>
              </div>
            </div>
          </div>
        </div>

        <!-- 价格统计信息 - 无数据状态 -->
        <div class="relative px-4 pb-4">
          <div class="grid grid-cols-3 gap-2">
            <div class="bg-white/50 rounded-lg p-2 text-center border border-gray-300/20">
              <div class="w-6 h-6 bg-gray-300/30 rounded-md flex items-center justify-center mx-auto mb-1">
                <text class="i-mdi-trending-down text-gray-400 text-xs"></text>
              </div>
              <div class="text-gray-500 text-xs mb-0.5">最低</div>
              <div class="text-gray-400 font-bold text-xs">--元</div>
            </div>
            <div class="bg-white/50 rounded-lg p-2 text-center border border-gray-300/20">
              <div class="w-6 h-6 bg-gray-300/30 rounded-md flex items-center justify-center mx-auto mb-1">
                <text class="i-mdi-trending-up text-gray-400 text-xs"></text>
              </div>
              <div class="text-gray-500 text-xs mb-0.5">最高</div>
              <div class="text-gray-400 font-bold text-xs">--元</div>
            </div>
            <div class="bg-white/50 rounded-lg p-2 text-center border border-gray-300/20">
              <div class="w-6 h-6 bg-gray-300/30 rounded-md flex items-center justify-center mx-auto mb-1">
                <text class="i-mdi-chart-line text-gray-400 text-xs"></text>
              </div>
              <div class="text-gray-500 text-xs mb-0.5">平均</div>
              <div class="text-gray-400 font-bold text-xs">--元</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 净值走势图 -->
    <div class="px-4">
      <div class="mb-4">
        <ly-title :title="`价格走势图`" />
      </div>
      <div class="bg-white rounded-lg shadow-sm px-2 py-4">
        <!-- 走势图标签栏 -->
        <div class="flex items-center justify-between mb-4 mt-2">
          <div class="flex gap-2 justify-start w-full">
            <div
              v-for="(period, index) in periods"
              :key="index"
              :class="['text-xs py-2 px-4 rounded-full transition-colors cursor-pointer', currentPeriod === period ? 'bg-theme-blue text-white' : 'bg-gray-100 text-gray-600']"
              @click="changePeriod(period)"
            >
              {{ period }}
            </div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="">
          <!-- 图表组件 -->
          <qiun-data-charts
            ref="priceLineChart"
            :ontouch="true"
            v-if="chartData.series && chartData.series.length > 0"
            type="line"
            :chartData="chartData"
            :opts="chartOpts"
            :canvas2d="true"
            canvasId="priceLineChart"
            tooltipFormat="medicinePriceLineChart"
            :onmovetip="true"
          />
          <div v-else class="text-center text-gray-500 px-4 py-4 flex flex-col items-center justify-center gap-2 text-sm">
            <text class="i-mdi-chart-line text-2xl"></text>
            <div>暂无价格走势图</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史价格对比 -->
    <div class="px-4 pt-4">
      <div class="mb-4">
        <ly-title :title="`历史价格对比`" />
        <div class="text-sm text-gray-500 mt-1">右滑显示更多历史数据</div>
      </div>
      <div class="overflow-hidden">
        <div class="relative bg-white rounded-lg shadow-sm overflow-hidden" v-if="availableYears.length > 0">
          <!-- 固定的月/年列 -->
          <div class="absolute left-0 top-0 z-10 bg-white border-r border-gray-300 shadow-sm">
            <!-- 固定表头 -->
            <div class="bg-theme-blue text-white px-3 py-3 text-xs text-center font-medium border-r border-blue-600 w-20">月/年</div>
            <!-- 固定表体 -->
            <div>
              <div
                v-for="(month, index) in priceComparisonData"
                :key="index"
                :class="['px-3 py-3 text-xs font-medium text-center text-gray-700 border-r border-b border-gray-200 w-20 last:border-b-0', index % 2 === 0 ? 'bg-blue-50' : 'bg-white']"
              >
                {{ month.month }}
              </div>
            </div>
          </div>

          <!-- 可滚动的年份数据区域 -->
          <scroll-view class="pl-20 box-border" scroll-x :scroll-into-view="lastYearId">
            <div class="min-w-max">
              <!-- 年份表头 -->
              <div class="bg-theme-blue text-white flex">
                <div v-for="year in availableYears" :key="year" class="px-3 py-3 text-xs text-center font-medium border-r border-blue-400 w-20 flex-shrink-0 last:border-r-0">
                  {{ year }}
                </div>
              </div>

              <!-- 年份数据表体 -->
              <div>
                <div v-for="(month, index) in priceComparisonData" :key="index" :class="['flex border-b text-xs border-gray-200 last:border-b-0', index % 2 === 0 ? 'bg-blue-50' : 'bg-white']">
                  <div v-for="year in availableYears" :key="year" :id="'year-data-' + year" class="px-3 py-3 text-center text-gray-700 border-r border-gray-200 w-20 flex-shrink-0 last:border-r-0">
                    {{ month.prices[year] || '-' }}
                  </div>
                </div>
              </div>
            </div>
          </scroll-view>
        </div>
      </div>
      <!-- 无数据提示 -->
      <div v-if="availableYears.length === 0" class="text-center text-gray-500 px-4 py-8 flex flex-col items-center justify-center gap-2">
        <text class="i-mdi-chart-timeline-variant text-4xl"></text>
        <div>暂无历史价格对比数据</div>
      </div>
    </div>
    <ly-line-bar :height="100" />
    <!-- 价格历史记录 
    <div class="p-4" >
      <div class="mb-4">
        <ly-title :title="`历史价格列表`" />
      </div>
      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex justify-between items-center mb-2">
          <div class="text-sm text-gray-600">日期</div>
          <div class="text-sm text-gray-600">价格</div>
        </div>
        <div class="space-y-2" v-if="priceHistory.length > 0">
          <div v-for="(item, index) in priceHistory" :key="index" class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
            <div class="text-sm text-gray-600">{{ item.date }}</div>
            <div class="flex items-center">
              <ly-price-format :price="item.price" />
               <div
                :class="['text-xs px-2 py-0.5 rounded-full', isPositive(item.change) ? 'bg-red-50 text-red-600' : isNegative(item.change) ? 'bg-green-50 text-green-600' : 'bg-gray-50 text-gray-500']"
              >
                {{ item.change }}
              </div> 
            </div>
          </div>
        </div>

        <div v-else class="text-center text-gray-500 px-4 py-4 flex flex-col items-center justify-center gap-2">
          <text class="i-mdi-information-outline mr-2 text-2xl"></text>
          <div>暂无价格历史记录</div>
        </div>
        <div v-if="priceHistory.length > 0" @click="toggleShowMoreHistory" class="text-theme-blue text-sm py-2 px-4 rounded-lg hover:bg-blue-50 transition-colors flex items-center justify-center gap-1 mx-auto">
          <text class="i-mdi-chevron-down"></text>
          <text>查看更多</text>
        </div>
      </div>
    </div>-->
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { MedicineTypes } from '@/monkey/types';

  // 当前行情
  const currentDetail = ref<MedicineTypes.MedicinalCurrentItemDetail>({});

  // 药材详情
  const detail = ref<
    MedicineTypes.MedicinalItem & {
      price?: string;
      change?: string;
      basePrice?: string;
      origin?: string;
      spec?: string;
      market?: string;
    }
  >({});

  const priceLineChart = ref<any>(null);

  // 药材ID
  const ycbm = ref<string>('');

  // 时间区间选择
  // const periods = ['日','近一周', '近一月', '近半年', '近一年', '全部'];
  // 日当日 月 近十年 全部 全部显示
  const periods = ['日', '月', '全部'];

  // 当前时间区间
  const currentPeriod = ref('月');

  // 图表数据
  const chartData = ref({
    categories: [],
    series: [],
  });

  // 价格历史数据
  const priceHistory = ref([]);

  // 原始价格列表数据
  const originalPriceList = ref([]);

  // 历史价格对比数据
  const priceComparisonData = ref([]);

  // 可用年份列表
  const availableYears = ref([]);

  // 可滚动年份区域的引用
  const lastYearId = ref<string>('');

  // 价格统计数据
  const priceStats = ref({
    minPrice: 0,
    maxPrice: 0,
    avgPrice: 0,
  });

  // 图表配置
  const chartOpts = ref({
    color: ['#3e8bf7', '#60a5fa', '#d1d5db'],
    padding: [10, 20, 0, 10],
    dataLabel: false,
    dataPointShape: false,
    enableScroll: false,
    legend: {
      show: false,
    },
    xAxis: {
      disableGrid: true,
      axisLine: true,
      labelCount: 7,
      fontSize: 10,
      fontColor: '#333',
      marginTop: 8,
      rotateLabel: true,
      rotateAngle: 60,
    },
    yAxis: {
      gridType: 'dash',
      dashLength: 4,
    },
    extra: {
      line: {
        type: 'straight',
        width: 2,
        activeType: 'hollow',
      },
    },
  });

  // 切换时间区间
  const changePeriod = (period) => {
    currentPeriod.value = period;
    generateChartData();
    generatePriceComparisonData();
  };

  // 获取日期范围
  const getDateRange = (period) => {
    const today = monkey.$dayjs();
    let startDate;

    switch (period) {
      case '日':
        startDate = today.subtract(1, 'day');
        break;
      case '月':
        startDate = today.subtract(10, 'year');
        break;
      case '近一周':
        startDate = today.subtract(7, 'day');
        break;
      case '近一月':
        startDate = today.subtract(1, 'month');
        break;
      case '近半年':
        startDate = today.subtract(6, 'month');
        break;
      case '近一年':
        startDate = today.subtract(1, 'year');
        break;
      default: // 全部
        // 对于"全部"，我们需要找到数据中最早的日期
        if (originalPriceList.value && originalPriceList.value.length > 0) {
          const earliestDate = originalPriceList.value.reduce((earliest, item) => {
            const itemDate = monkey.$dayjs(item.jgtjrq);
            return !earliest || itemDate.isBefore(earliest) ? itemDate : earliest;
          }, null);
          startDate = earliestDate || today.subtract(50, 'year'); // 如果没有数据，默认50年前
        } else {
          startDate = today.subtract(50, 'year'); // 默认50年前
        }
        break;
    }

    console.log('🚀 ~ getDateRange ~ end:', today.format('YYYY-MM-DD'));
    console.log('🚀 ~ getDateRange ~ start:', startDate.format('YYYY-MM-DD'));
    return {
      start: startDate.format('YYYY-MM-DD'),
      end: today.format('YYYY-MM-DD'),
    };
  };

  // 生成图表数据
  const generateChartData = () => {
    if (!originalPriceList.value || originalPriceList.value.length === 0) {
      console.log('没有价格数据可用');
      return;
    }

    const { start, end } = getDateRange(currentPeriod.value);

    console.log(`🚀 生成图表数据 - 时间范围: ${start} 到 ${end}, 当前周期: ${currentPeriod.value}`);
    console.log(`🚀 原始数据总数: ${originalPriceList.value.length}`);

    // 筛选时间范围内的数据
    const filteredData = originalPriceList.value.filter((item) => {
      const itemDate = monkey.$dayjs(item.jgtjrq);
      return (itemDate.isAfter(start) && itemDate.isBefore(end)) || itemDate.isSame(start) || itemDate.isSame(end);
    });

    console.log(`🚀 筛选后数据数量: ${filteredData.length}`);

    // 按日期排序
    const sortedData = [...filteredData].sort((a, b) => monkey.$dayjs(a.jgtjrq).valueOf() - monkey.$dayjs(b.jgtjrq).valueOf());

    // 价格数据
    const dates = sortedData.map((item) => monkey.$dayjs(item.jgtjrq).format('YYYY-MM-DD'));
    const prices = sortedData.map((item) => Number(item.jg || 0));

    // 计算移动平均线 (7天)
    const avgPrices = [];
    for (let i = 0; i < prices.length; i++) {
      if (i < 6) {
        // 不足7天的数据点，取前i+1天平均
        const slice = prices.slice(0, i + 1);
        const avg = slice.reduce((a, b) => a + b, 0) / slice.length;
        avgPrices.push(Number(avg.toFixed(2)));
      } else {
        // 取前7天移动平均
        const slice = prices.slice(i - 6, i + 1);
        const avg = slice.reduce((a, b) => a + b, 0) / 7;
        avgPrices.push(Number(avg.toFixed(2)));
      }
    }

    // 计算同期历史价格(去年同期)
    const lastYearPrices = sortedData.map((item) => {
      const lastYearDate = monkey.$dayjs(item.jgtjrq).subtract(1, 'year').format('YYYY-MM-DD');
      const lastYearItem = originalPriceList.value.find((d) => d.jgtjrq === lastYearDate);
      return lastYearItem ? Number(lastYearItem.jg || 0) : null;
    });

    // 过滤掉null值的同期价格
    const filteredLastYearPrices = lastYearPrices.some((price) => price !== null) ? lastYearPrices : Array(dates.length).fill(0); // 如果没有去年同期数据，使用0填充

    // 更新图表数据
    chartData.value = {
      categories: dates,
      series: [
        {
          name: '本期价格',
          data: prices,
        },
        // {
        //   name: '去年同期',
        //   data: filteredLastYearPrices,
        // },
        // {
        //   name: '7日均价',
        //   data: avgPrices,
        // },
      ],
    };

    // 同时更新价格历史数据
    updatePriceHistory(sortedData);
  };

  /**
   * 更新价格历史记录
   * @param sortedData 排序后的数据
   * @returns 返回价格历史记录
   */
  const updatePriceHistory = (sortedData) => {
    // 取最近5条记录
    const recentData = sortedData.slice(-10).reverse();

    // 计算价格变化百分比
    priceHistory.value = recentData.map((item, index, arr) => {
      const currentPrice = Number(item.jg || 0);
      let change = '0.00%';

      // 计算与前一天相比的变化百分比
      if (index < arr.length - 1) {
        const prevPrice = Number(arr[index + 1].jg || 0);
        if (prevPrice > 0) {
          const changeValue = ((currentPrice - prevPrice) / prevPrice) * 100;
          const sign = changeValue >= 0 ? '+' : '';
          change = `${sign}${changeValue.toFixed(2)}%`;
        }
      }

      return {
        date: monkey.$dayjs(item.jgtjrq).format('YYYY-MM-DD'),
        price: currentPrice.toFixed(2),
        change: change,
      };
    });
  };

  /**
   * 计算价格统计数据
   * @returns 返回价格统计数据
   */
  const calculatePriceStats = () => {
    if (!originalPriceList.value || originalPriceList.value.length === 0) {
      priceStats.value = {
        minPrice: 0,
        maxPrice: 0,
        avgPrice: 0,
      };
      return;
    }

    // 提取所有有效价格
    const validPrices = originalPriceList.value.map((item) => Number(item.jg || 0)).filter((price) => price > 0);

    if (validPrices.length === 0) {
      priceStats.value = {
        minPrice: 0,
        maxPrice: 0,
        avgPrice: 0,
      };
      return;
    }

    // 计算最低价、最高价和平均价
    const minPrice = Math.min(...validPrices);
    const maxPrice = Math.max(...validPrices);
    const avgPrice = validPrices.reduce((sum, price) => sum + price, 0) / validPrices.length;

    priceStats.value = {
      minPrice: Number(minPrice.toFixed(2)),
      maxPrice: Number(maxPrice.toFixed(2)),
      avgPrice: Number(avgPrice.toFixed(2)),
    };
  };

  // 在数据更新后设置要滚动到的元素ID
  const scrollToLastYear = () => {
    lastYearId.value  = '';
    nextTick(() => {
      if (availableYears.value && availableYears.value.length > 0) {
        // 设置最后一个年份的ID
        const lastYear = availableYears.value[availableYears.value.length - 1];
        lastYearId.value = 'year-data-' + lastYear;
        console.log('🚀 ~ scrollToLastYear ~ lastYearId.value:', lastYearId.value);
      }
    });
  };

  /**
   * 生成历史价格对比数据
   * 该函数处理原始价格列表，生成按年份、月份组织的价格对比表格数据
   * 最终数据结构将用于渲染类似"历史价格对比"表格
   */
  const generatePriceComparisonData = () => {
    // 检查是否有可用的价格数据
    if (!originalPriceList.value || originalPriceList.value.length === 0) {
      priceComparisonData.value = [];
      availableYears.value = [];
      return;
    }

    // 获取当前选定时间范围
    const { start, end } = getDateRange(currentPeriod.value);

    // 筛选时间范围内的数据
    const filteredData = originalPriceList.value.filter((item) => {
      const itemDate = monkey.$dayjs(item.jgtjrq);
      return (itemDate.isAfter(start) && itemDate.isBefore(end)) || itemDate.isSame(start) || itemDate.isSame(end);
    });

    // 定义月份列表，用于表格行标题
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

    // 创建集合存储所有年份，用于去重
    const yearsSet = new Set();
    // 创建对象用于按年月分组存储价格数据
    const groupedData = {};

    // 遍历已筛选的价格数据，按年月分组
    filteredData.forEach((item) => {
      // 解析日期获取年份和月份
      const date = monkey.$dayjs(item.jgtjrq);
      const year = date.year();
      const month = date.month() + 1; // dayjs的月份从0开始，需+1调整
      const price = Number(item.jg || 0);

      // 过滤掉价格为0的无效数据
      if (price > 0) {
        // 记录有效年份
        yearsSet.add(year);

        // 初始化年份和月份的数据结构
        if (!groupedData[year]) {
          groupedData[year] = {};
        }
        if (!groupedData[year][month]) {
          groupedData[year][month] = [];
        }
        // 保存价格和日期信息
        groupedData[year][month].push({
          price,
          date: item.jgtjrq,
        });
      }
    });

    // 将年份集合转为数组并降序排序（最近年份排在前面）
    const sortedYears = Array.from(yearsSet).sort((a, b) => a - b);
    availableYears.value = sortedYears;

    // // 获取最后的三个年份
    // const lastThreeYears = sortedYears.slice(-3); // 取最后三个并反转，使最新的在前面

    // // 重新排序年份，将最后的三个年份放在最前面
    // const reorderedYears = [...lastThreeYears, ...sortedYears.filter(year => !lastThreeYears.includes(year))];

    // // 更新可用年份列表
    // availableYears.value = reorderedYears;

    // 初始化表格数据结构，每个月一行
    const comparisonData = months.map((month) => ({
      month, // 月份名称
      prices: {}, // 用于存储不同年份该月的价格
    }));

    // 计算每个月在各年份的最新价格
    Object.keys(groupedData).forEach((year) => {
      Object.keys(groupedData[year]).forEach((month) => {
        const monthData = groupedData[year][month];
        if (monthData.length > 0) {
          // 按日期降序排序，取每月最新的价格记录
          monthData.sort((a, b) => monkey.$dayjs(b.date).valueOf() - monkey.$dayjs(a.date).valueOf());
          const latestPrice = monthData[0].price;

          // 将价格添加到对应月份行的对应年份列
          const monthIndex = parseInt(month) - 1;
          comparisonData[monthIndex].prices[year] = latestPrice.toFixed(0);
        }
      });
    });

    // 更新价格对比数据，用于视图渲染
    priceComparisonData.value = comparisonData;
    // if (availableYears.value.length > 0) {
    //   scrollLeft.value = '';
    //   scrollLeft.value = 10000;
    // }
    scrollToLastYear();
  };

  /**
   * 获取药材详情
   */
  const getMedicinalItemDetail = async () => {
    try {
      const { errcode, data } = await monkey.$api.medicine.getMedicinalItemDetail(ycbm.value);
      if (errcode == 0) {
        console.log('🚀 ~ getMedicinalItemDetail ~ data:', data);

        currentDetail.value = data.medicinalPriceToday;

        // 存储价格列表数据
        originalPriceList.value = data.medicinalPriceList || [];

        // 调试信息：显示数据的日期范围
        if (originalPriceList.value.length > 0) {
          const dates = originalPriceList.value.map((item) => item.jgtjrq).sort();
          console.log(`🚀 价格数据日期范围: ${dates[0]} 到 ${dates[dates.length - 1]}`);
          console.log(`🚀 价格数据总数: ${originalPriceList.value.length}`);

          // 显示前几条和后几条数据
          console.log('🚀 前3条数据:', originalPriceList.value.slice(0, 3));
          console.log('🚀 后3条数据:', originalPriceList.value.slice(-3));
        }

        // 设置详情数据
        detail.value = {
          ...data,
          jg: data.medicinalPriceToday?.jg || '0.00',
        };

        // 生成图表数据
        generateChartData();

        // 计算价格统计数据
        calculatePriceStats();

        // 生成历史价格对比数据
        generatePriceComparisonData();
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * 切换查看更多历史价格
   */
  const toggleShowMoreHistory = () => {
    monkey.$router.navigateTo('/modules/home/<USER>/history?ycbm=' + ycbm.value);
  };

  // 判断涨跌幅是否为正数、负数或零
  const isPositive = (change: string) => change?.startsWith('+') && change !== '+0.00%';
  const isNegative = (change: string) => change?.startsWith('-') && change !== '-0.00%';
  const isZero = (change: string) => change === '+0.00%' || change === '-0.00%' || change === '0.00%' || change === '+0.0%' || change === '-0.0%' || change === '0.0%';

  onLoad((options) => {
    ycbm.value = options.ycbm;
    getMedicinalItemDetail();
  });

  onMounted(() => {
    console.log(' yearScrollContainer.value', getCurrentInstance()?.refs.yearScrollContainer);
  });
</script>
