<template>
  <ly-layout>
    <div class="p-4">
      <div class="text-2xl font-bold mb-4 text-gray-800 border-b border-gray-200 pb-2">{{ bannerData.tpmc }}</div>
      <div class="text-gray-600 text-sm mb-2 flex flex-col sm:flex-row justify-between gap-2">
        <text>创建时间: {{ monkey.$dayjs(bannerData.createTime).format('YYYY-MM-DD HH:mm:ss') }}</text>
        <text v-if="bannerData.creater.length > 0 && bannerData.creater[0].name" class="font-medium">系统管理员: {{ bannerData.creater[0].name }}</text>
      </div>
      <div class="mt-4 text-gray-700 content w-full" v-html="content"></div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import type { HomeTypes } from '@/monkey/types';

  // banner 数据
  const bannerData = ref<HomeTypes.BannerData>({});

  // 内容
  const content = ref('');

  // 获取 banner 详情
  const getBannerDetail = async (id: string) => {
    try {
      const params = monkey.$helper.param.getAuthineFormParams({
        objectId: id,
        schemaCode: 'lbtgl', // 轮播图管理
      });
      const { errcode, data } = await monkey.$api.authine.getAuthineForm(params);
      if (errcode === 0 && data) {
        bannerData.value = data.bizObject.data;
        if (data.bizObject.data.zsnr) {
          content.value = data.bizObject.data.zsnr.replace(/<img/g, '<img style="max-width:100%; object-fit: fill;"');
        } else content.value = '暂无内容';
      }
    } catch (error) {
      console.error('获取banner详情失败:', error);
      monkey.$helper.toast.showToast('获取内容失败');
    }
  };

  // 页面加载时获取数据
  onLoad((options) => {
    const id = options.id;
    const name = options.name;
    uni.setNavigationBarTitle({
      title: name,
    });
    if (id) {
      getBannerDetail(id);
    }
  });
</script>

<style scoped lang="scss">
  .content {
    :deep(img) {
      width: 100%;
      height: auto;
      display: block;
      margin: 10px 0;
    }
  }
</style>
