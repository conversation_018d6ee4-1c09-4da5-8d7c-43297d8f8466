<template>
  <ly-layout>
    <view class="p-4">
      <view class="bg-white rounded-lg shadow-sm p-4 mb-4">
        <view class="text-lg font-medium mb-4">出库预约</view>
        
        <uni-forms ref="formRef" :model="formData" :rules="rules" label-position="top">
          <!-- 基本信息 -->
          <view class="space-y-3 mb-6">
            <view class="text-base font-medium border-l-4 border-blue-500 pl-2 mb-3">基本信息</view>
            
            <view class="grid grid-cols-1 gap-4">
              <!-- 出库日期 -->
              <uni-forms-item name="reservationDate" label="预约日期" required>
                <uni-datetime-picker 
                  type="date"
                  v-model="formData.reservationDate"
                  :clear-icon="false"
                  class="w-full"
                />
              </uni-forms-item>
              
              <!-- 出库时间段 -->
              <uni-forms-item name="timeSlot" label="预约时间段" required>
                <uni-data-checkbox
                  v-model="formData.timeSlot"
                  :localdata="timeSlotOptions"
                  mode="tag"
                />
              </uni-forms-item>
              
              <!-- 联系人 -->
              <uni-forms-item name="contactName" label="联系人" required>
                <uni-easyinput
                  v-model="formData.contactName"
                  placeholder="请输入联系人姓名"
                  trim="both"
                />
              </uni-forms-item>
              
              <!-- 联系电话 -->
              <uni-forms-item name="contactPhone" label="联系电话" required>
                <uni-easyinput
                  v-model="formData.contactPhone"
                  placeholder="请输入联系电话"
                  trim="both"
                />
              </uni-forms-item>
            </view>
          </view>
          
          <!-- 出库产品 -->
          <view class="space-y-3 mb-6">
            <view class="text-base font-medium border-l-4 border-blue-500 pl-2 mb-3">出库产品</view>
            
            <uni-forms-item name="items" label="" :error-message="itemsErrorMsg">
              <view @click="showProductSelector" v-if="formData.items.length === 0" class="border border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
                <view class="flex flex-col items-center justify-center">
                  <view class="add-icon mb-2">
                    <text class="iconfont icon-add"></text>
                  </view>
                  <view class="text-sm text-gray-500">选择出库产品</view>
                </view>
              </view>
              
              <!-- 已选产品列表 -->
              <view v-else class="space-y-2">
                <view v-for="(item, index) in formData.items" :key="index" class="bg-white border border-gray-200 rounded-lg p-3 flex justify-between items-center shadow-sm">
                  <view class="flex items-center">
                    <image :src="item.image" class="w-12 h-12 bg-gray-200 rounded" mode="aspectFill"></image>
                    <view class="ml-3">
                      <view class="text-sm font-medium">{{item.name}}</view>
                      <view class="text-xs text-gray-500">{{item.stock}}{{item.unit}} · {{item.location}}</view>
                    </view>
                  </view>
                  <view class="flex items-center">
                    <uni-number-box 
                      v-model="item.quantity" 
                      :min="1" 
                      :max="99"
                      class="mr-2"
                    />
                    <text class="text-red-500 iconfont icon-delete" @click="removeItem(index)">删除</text>
                  </view>
                </view>
                
                <view class="mt-2 flex justify-center">
                  <button @click="showProductSelector" class="bg-blue-50 text-blue-500 rounded-full px-4 py-1 flex items-center text-sm">
                    <text class="iconfont icon-add mr-1"></text>添加更多产品
                  </button>
                </view>
              </view>
            </uni-forms-item>
          </view>
          
          <!-- 备注 -->
          <view class="space-y-3">
            <view class="text-base font-medium border-l-4 border-blue-500 pl-2 mb-3">备注信息</view>
            
            <uni-forms-item name="remark" label="">
              <uni-easyinput
                v-model="formData.remark"
                type="textarea"
                placeholder="请输入备注信息（选填）"
                :maxlength="200"
                :autoHeight="true"
                class="bg-white"
              />
            </uni-forms-item>
          </view>
        </uni-forms>
      </view>
      
      <!-- 底部按钮 -->
      <view class="mt-8 pb-10">
        <button @click="submitForm" class="w-full bg-blue-500 text-white rounded-lg py-3 font-medium shadow-md hover:bg-blue-600 active:bg-blue-700">提交出库预约</button>
      </view>
    </view>
    
    <!-- 产品选择器弹窗 -->
    <!-- <uni-popup ref="productSelector" type="bottom">
      <view class="bg-white rounded-t-xl p-4 max-h-[70vh]">
        <view class="flex justify-between items-center mb-4">
          <text class="text-lg font-medium">选择产品</text>
          <text class="iconfont icon-close" @click="hideProductSelector"></text>
        </view>
        
        <view class="uni-search-box mb-4">
          <uni-search-bar v-model="searchKeyword" placeholder="搜索产品" @confirm="searchProducts" @cancel="searchKeyword = ''"/>
        </view>
        
        <scroll-view scroll-y style="max-height: 50vh;">
          <view class="space-y-3">
            <view v-for="product in availableProducts" :key="product.id" 
                  class="flex justify-between items-center p-3 border-b border-gray-100">
              <view class="flex items-center">
                <image :src="product.image" class="w-10 h-10 bg-gray-200 rounded" mode="aspectFill"></image>
                <view class="ml-2">
                  <view class="text-sm font-medium">{{product.name}}</view>
                  <view class="text-xs text-gray-500">库存: {{product.stock}}{{product.unit}} · {{product.location}}</view>
                </view>
              </view>
              <button @click="selectProduct(product)" 
                      class="bg-blue-500 text-white rounded-full px-3 py-1 text-xs">
                选择
              </button>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup> -->
  </ly-layout>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { onReady } from '@dcloudio/uni-app';

// 表单引用
const formRef = ref();

// 产品选择弹窗引用
const productSelector = ref();

// 表单数据
const formData = reactive({
  reservationDate: '',
  timeSlot: '',
  contactName: '',
  contactPhone: '',
  items: [],
  remark: ''
});

// 时间段选项
const timeSlotOptions = [
  { text: '上午 (9:00-12:00)', value: 'morning' },
  { text: '下午 (13:00-17:00)', value: 'afternoon' },
  { text: '晚上 (18:00-21:00)', value: 'evening' }
];

// 表单验证规则
const rules = {
  reservationDate: {
    rules: [
      { required: true, errorMessage: '请选择预约日期' }
    ]
  },
  timeSlot: {
    rules: [
      { required: true, errorMessage: '请选择预约时间段' }
    ]
  },
  contactName: {
    rules: [
      { required: true, errorMessage: '请输入联系人姓名' },
      { minLength: 2, maxLength: 20, errorMessage: '联系人姓名长度在2-20个字符之间' }
    ]
  },
  contactPhone: {
    rules: [
      { required: true, errorMessage: '请输入联系电话' },
      { pattern: /^1[3-9]\d{9}$/, errorMessage: '请输入正确的手机号码' }
    ]
  },
  items: {
    rules: [
      { 
        validateFunction: (rule, value, data, callback) => {
          if (value.length === 0) {
            callback('请至少选择一件产品');
          }
          return true;
        }
      }
    ]
  }
};

// 产品列表错误信息
const itemsErrorMsg = ref('');

// 可选产品列表
const availableProducts = ref([
  {
    id: '1',
    name: '东北大米 五常稻花香',
    quantity: 1,
    unit: 'kg',
    stock: 850,
    location: 'B区-02-05货架',
    image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'
  },
  {
    id: '2',
    name: '优质花生米 精选大粒',
    quantity: 1,
    unit: 'kg',
    stock: 1200,
    location: 'A区-01-03货架',
    image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'
  },
  {
    id: '3',
    name: '有机玉米 非转基因',
    quantity: 1,
    unit: 'kg',
    stock: 600,
    location: 'C区-03-01货架',
    image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'
  },
  {
    id: '4',
    name: '高粱米 红高粱',
    quantity: 1,
    unit: 'kg',
    stock: 450,
    location: 'B区-01-08货架',
    image: 'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'
  }
]);

// 搜索关键词
const searchKeyword = ref('');

// 组件初始化完成后执行
onReady(() => {
  // 初始化表单
});

// 显示产品选择器
const showProductSelector = () => {
  productSelector.value.open('bottom');
};

// 隐藏产品选择器
const hideProductSelector = () => {
  productSelector.value.close();
};

// 选择产品
const selectProduct = (product) => {
  // 检查是否已经选择了该产品
  const existIndex = formData.items.findIndex(item => item.id === product.id);
  
  if (existIndex === -1) {
    // 如果没有选择过，添加到列表
    formData.items.push({...product});
  } else {
    // 如果已经选择过，增加数量
    formData.items[existIndex].quantity += 1;
  }
  
  hideProductSelector();
  itemsErrorMsg.value = '';
};

// 移除产品
const removeItem = (index) => {
  formData.items.splice(index, 1);
};

// 搜索产品
const searchProducts = () => {
  // TODO: 实现搜索逻辑
  console.log('搜索产品:', searchKeyword.value);
};

// 提交表单
const submitForm = () => {
  formRef.value.validate().then(res => {
    console.log('表单数据验证通过', formData);
    
    // 显示加载提示
    uni.showLoading({
      title: '提交中...'
    });
    
    // 模拟请求
    setTimeout(() => {
      // 隐藏加载提示
      uni.hideLoading();
      
      // 显示成功提示
      uni.showToast({
        title: '预约成功',
        icon: 'success',
        duration: 2000
      });
      
      // 重置表单
      formRef.value.resetFields();
      formData.items = [];
      
      // TODO: 跳转到预约列表或详情页
    }, 1500);
  }).catch(err => {
    console.log('表单数据验证失败', err);
  });
};
</script>

<style lang="scss" scoped>
.add-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #EBF5FF;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .icon-add {
    color: #3B82F6;
    font-size: 24px;
  }
}

:deep(.uni-forms-item__label) {
  font-size: 14px;
  color: #4B5563;
  margin-bottom: 4px;
}

:deep(.uni-easyinput__content) {
  background-color: #F9FAFB;
  border-radius: 6px;
  height: 40px;
}

:deep(.uni-data-checkbox) {
  .uni-data-checkbox-item {
    margin-right: 8px;
    margin-bottom: 8px;
  }
  
  .checklist-text {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
  }
  
  .is-checked {
    .checklist-text {
      background-color: #EBF5FF;
      color: #3B82F6;
      border-color: #3B82F6;
    }
  }
}

:deep(.uni-numbox) {
  height: 30px;
  
  .uni-numbox__minus,
  .uni-numbox__plus {
    background-color: #F9FAFB;
    width: 30px;
  }
  
  .uni-numbox__value {
    width: 40px;
    background-color: #F9FAFB;
  }
}

:deep(.uni-popup__wrapper) {
  border-radius: 16px 16px 0 0;
}
</style>