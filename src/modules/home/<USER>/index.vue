<template>
  <ly-layout>
    <div class="px-4 bg-white">
      <uni-search-bar v-model="searchValue" placeholder="请输入产品名称或拼音" @confirm="handleSearch" @clear="handleClear" cancelButton="none" />
    </div>
    <div v-if="indexList.length === 0" class="h-750 flex items-center justify-center">
      <van-empty description="暂无数据" :image="monkey.$url.cdn(monkey.$config.empty.noData)" />
    </div>
    <div v-else class="flex-1 px-4 pt-2 pr-5 box-border bg-gradient-to-br from-gray-50 to-blue-50">
      <van-index-bar :index-list="indexList" highlight-color="#3e8bf7">
        <view v-for="item in listLetter" :key="item.letter">
          <van-index-anchor :index="item.letter" class="text-gray-800" />
          <div class="grid grid-cols-4 gap-3 my-2">
            <div v-for="item in item.data" :key="item.id" @click="handleItemClick(item)" class="bg-white rounded-lg p-3 shadow-sm active:bg-green-200/30 flex flex-col">
              <div class="text-sm font-medium text-gray-800">{{ item.bzmc }}</div>
              <div class="text-xs text-gray-500 mt-1 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
            </div>
          </div>
        </view>
      </van-index-bar>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { MedicinalItem } from '@/monkey/types';

  // 药材列表
  const list = ref<MedicinalItem[]>([]);

  // 药材列表
  const listLetter = ref<MedicinalItem[]>([]);

  // 索引列表
  const indexList = ref<string[]>([]);

  // 搜索值
  const searchValue = ref<string>('');

  /**
   * 从ywsc字段中提取拼音并生成首字母缩写
   * @param ywsc ywsc字段内容，格式如：鸡内金,jnj,鸡黄皮,鸡盹皮
   * @returns 拼音信息对象
   */
  const extractPinyinFromYwsc = (ywsc: string) => {
    if (!ywsc) return { original: '', initials: '' };

    const ywscParts = ywsc.split(',');

    // 尝试找到拼音缩写（通常是纯小写字母的部分）
    for (const part of ywscParts) {
      const trimmedPart = part.trim();
      // 检查是否是拼音缩写（纯小写字母）
      if (/^[a-z]+$/.test(trimmedPart)) {
        // 简单的首字母提取：按2个字符一组提取首字母
        let initials = '';
        for (let i = 0; i < trimmedPart.length; i += 2) {
          if (i < trimmedPart.length) {
            initials += trimmedPart.charAt(i);
          }
        }

        return {
          original: trimmedPart,
          initials: initials,
        };
      }
    }

    return { original: '', initials: '' };
  };

  /**
   * 获取药材列表
   */
  const getMedicinalItemList = async () => {
    try {
      const { errcode, data } = await monkey.$api.medicine.getMedicinalItemList();
      if (errcode == 0) {
        console.log('🚀 ~ getMedicinalItemList ~ data:', data);
        // 将数据转换为MedicinalItem类型，并提取拼音字段
        const enrichedData = data.map((item: MedicinalItem) => {
          const pinyinInfo = extractPinyinFromYwsc(item.ywsc);
          return {
            ...item,
            pinyin: pinyinInfo.original, // 原始拼音字符串
            pinyinInitials: pinyinInfo.initials, // 拼音首字母缩写
          };
        });

        list.value = enrichedData;

        // 将数据转换为索引列表
        listLetter.value = createLetterList(enrichedData);
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * 创建索引列表
   * @param data 药材列表
   * @returns
   */
  const createLetterList = (data: MedicinalItem[]) => {
    // 按首字母分组
    const groupedData = {};

    data.forEach((item) => {
      // 使用已提取的拼音或产品名称的首字母
      const firstLetter = (item.pinyin && item.pinyin.charAt(0).toUpperCase()) || item.bzmc.charAt(0).toUpperCase();

      // 如果该首字母组不存在，则创建
      if (!groupedData[firstLetter]) {
        groupedData[firstLetter] = [];
      }

      // 将药材添加到对应首字母组
      groupedData[firstLetter].push(item);
    });

    // 转换为list格式
    const formattedList = Object.keys(groupedData)
      .sort()
      .map((letter) => ({
        letter,
        data: groupedData[letter],
      }));

    // 索引列表
    indexList.value = formattedList.map((item) => item.letter);
    return formattedList;
  };

  /**
   * 过滤列表
   * @param list 药材列表
   * @returns
   */
  const filterList = (list: MedicinalItem[]) => {
    const searchTerm = searchValue.value.toLowerCase().trim();

    if (!searchTerm) {
      return list;
    }

    return list.filter((item) => {
      // 匹配产品名称
      const nameMatch = item.bzmc.toLowerCase().includes(searchTerm);

      // 匹配完整拼音
      const fullPinyinMatch = item.pinyin && item.pinyin.toLowerCase().includes(searchTerm);

      // 匹配拼音首字母缩写
      const initialsMatch = item.pinyinInitials && item.pinyinInitials.toLowerCase().includes(searchTerm);

      // 拼音字符分解匹配：将拼音分解成单个字符，然后检查搜索词是否能在拼音中连续匹配
      let pinyinCharMatch = false;
      if (item.pinyin && searchTerm.length > 1) {
        const pinyinLower = item.pinyin.toLowerCase();
        // 检查搜索词的每个字符是否能在拼音中按顺序找到
        let pinyinIndex = 0;
        let searchIndex = 0;

        while (pinyinIndex < pinyinLower.length && searchIndex < searchTerm.length) {
          if (pinyinLower[pinyinIndex] === searchTerm[searchIndex]) {
            searchIndex++;
          }
          pinyinIndex++;
        }

        // 如果搜索词的所有字符都能按顺序在拼音中找到
        pinyinCharMatch = searchIndex === searchTerm.length;
      }

      const result = nameMatch || fullPinyinMatch || initialsMatch || pinyinCharMatch;
      return result;
    });
  };

  /**
   * 搜索
   */
  const handleSearch = () => {
    console.log('🚀 ~ handleSearch ~ searchValue:', searchValue.value);
    const result = filterList(list.value);
    if (result.length > 0) {
      // 过滤列表
      listLetter.value = createLetterList(result);
    } else {
      monkey.$helper.toast.warning('暂无数据');
    }
  };

  /**
   * 清空搜索
   */
  const handleClear = () => {
    listLetter.value = createLetterList(list.value);
  };

  /**
   * 点击药材项
   * @param item 药材项
   */
  const handleItemClick = (item: MedicinalItem) => {
    monkey.$router.navigateTo(`/modules/home/<USER>/detail?ycbm=${item.ycbm}`);
  };

  onLoad(() => {
    getMedicinalItemList();
  });
</script>

<style lang="scss" scoped></style>
