<template>
  <ly-layout is-arc-bg :active="1">
    <!-- 仓库列表 可点击我的仓库 查看我的货物 -->
    <div class="px-4 pt-4 pb-6">
      <div v-for="item in 10" :key="item" class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-200">
        <div class="p-4">
          <div class="text-lg font-semibold text-gray-800 flex-1">{{ item }}</div>
        </div>
      </div>
    </div>
    <!-- 货物列表 -->
    <div class="px-4 pb-4 space-y-3">
      <div v-if="goodsList.length > 0" class="space-y-3">
        <div v-for="item in goodsList" :key="item.id" class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-200">
          <!-- 货物基本信息 -->
          <div class="p-4">
            <!-- 货物名称和状态 -->
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-lg font-semibold text-gray-800 flex-1">{{ item.name }}</h3>
              <span
                :class="[
                  'text-xs px-2 py-1 rounded-full',
                  item.status === 'normal' ? 'bg-theme-green/10 text-theme-green' : item.status === 'expiring' ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600',
                ]"
              >
                {{ getStatusText(item.status) }}
              </span>
            </div>

            <!-- 货物详细信息网格 -->
            <div class="grid grid-cols-2 gap-3 mb-4">
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-theme-blue rounded-full"></div>
                <span class="text-sm text-gray-600">数量:</span>
                <span class="text-sm font-medium text-gray-800">{{ item.quantity }} {{ item.unit }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-theme-green rounded-full"></div>
                <span class="text-sm text-gray-600">重量:</span>
                <span class="text-sm font-medium text-gray-800">{{ item.weight }} kg</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span class="text-sm text-gray-600">仓库:</span>
                <span class="text-sm font-medium text-gray-800">{{ item.warehouse }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span class="text-sm text-gray-600">货位:</span>
                <span class="text-sm font-medium text-gray-800">{{ item.location }}</span>
              </div>
            </div>

            <!-- 日期信息 -->
            <div class="grid grid-cols-2 gap-3 mb-4">
              <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-xs text-gray-500 mb-1">存入日期</div>
                <div class="text-sm font-medium text-gray-800">{{ item.storageDate }}</div>
              </div>
              <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-xs text-gray-500 mb-1">到期日期</div>
                <div class="text-sm font-medium" :class="isExpiringSoon(item.expiryDate) ? 'text-red-600' : 'text-gray-800'">
                  {{ item.expiryDate }}
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="bg-gray-50 px-4 py-3 border-t border-gray-100">
            <div class="flex gap-2">
              <button
                @click="handleViewGoods(item)"
                class="flex-1 bg-theme-blue text-white text-sm font-medium py-2 px-4 rounded-lg hover:bg-theme-blue-600 transition-colors duration-200 active:scale-95"
              >
                看货
              </button>
              <button
                @click="handleOutbound(item)"
                class="flex-1 bg-theme-green text-white text-sm font-medium py-2 px-4 rounded-lg hover:bg-theme-green-600 transition-colors duration-200 active:scale-95"
              >
                出库
              </button>
              <button @click="handlePledge(item)" class="flex-1 bg-gray-600 text-white text-sm font-medium py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-200 active:scale-95">
                仓库质押
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="bg-white rounded-xl shadow-sm p-8 text-center">
        <div class="text-gray-400 mb-3">
          <i class="i-mdi-warehouse text-6xl"></i>
        </div>
        <div class="text-gray-500 text-base mb-2">暂无货物</div>
        <div class="text-gray-400 text-sm">您的仓库中还没有存储任何货物</div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  interface GoodsItem {
    id: string;
    name: string;
    quantity: number;
    unit: string;
    weight: number;
    warehouse: string;
    location: string;
    storageDate: string;
    expiryDate: string;
    status: 'normal' | 'expiring' | 'expired';
  }

  const goodsList = ref<GoodsItem[]>([
    {
      id: '1',
      name: '当归',
      quantity: 500,
      unit: 'kg',
      weight: 500,
      warehouse: 'A区仓库',
      location: 'A-01-001',
      storageDate: '2024-01-15',
      expiryDate: '2025-01-15',
      status: 'normal',
    },
    {
      id: '2',
      name: '川芎',
      quantity: 300,
      unit: 'kg',
      weight: 300,
      warehouse: 'B区仓库',
      location: 'B-02-015',
      storageDate: '2024-02-20',
      expiryDate: '2024-12-20',
      status: 'expiring',
    },
    {
      id: '3',
      name: '白芍',
      quantity: 200,
      unit: 'kg',
      weight: 200,
      warehouse: 'A区仓库',
      location: 'A-03-008',
      storageDate: '2023-12-10',
      expiryDate: '2024-06-10',
      status: 'expired',
    },
  ]);

  const getStatusText = (status: string) => {
    switch (status) {
      case 'normal':
        return '正常';
      case 'expiring':
        return '即将到期';
      case 'expired':
        return '已过期';
      default:
        return '未知';
    }
  };

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30; // 30天内到期
  };

  const handleViewGoods = (item: GoodsItem) => {
    console.log('看货:', item);
    monkey.$helper.toast.success('查看货物详情');
  };

  const handleOutbound = (item: GoodsItem) => {
    console.log('出库:', item);
    monkey.$helper.toast.success('申请出库');
  };

  const handlePledge = (item: GoodsItem) => {
    console.log('仓库质押:', item);
    monkey.$helper.toast.success('申请质押');
  };

  onLoad(() => {
    console.log('我的仓库页面加载');
  });
</script>

<style lang="scss" scoped></style>
