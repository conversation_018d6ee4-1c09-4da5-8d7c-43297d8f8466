<template>
  <ly-layout is-arc-bg>
    <div class="px-4 relative z-10 mt-4">
      <!-- 数据统计卡片 -->
      <div class="bg-white rounded-xl p-4 shadow-sm mb-5">
        <!-- 数据更新时间 -->
        <div class="flex justify-between items-center mb-4 pb-3 border-b border-gray-100">
          <text class="font-medium text-gray-700">实时更新数据</text>
          <div class="text-sm text-gray-500">2025-07-03 10:00:00</div>
        </div>
        <!-- 主要指标 -->
        <div class="flex justify-between items-center mb-4">
          <div class="text-center flex-1">
            <div class="text-gray-600 text-sm mb-1">累计总金额</div>
            <div class="text-2xl font-bold text-blue-600">* * * *</div>
            <div class="text-xs text-gray-500">万元</div>
          </div>
          <div class="w-px h-12 bg-gray-200 mx-3"></div>
          <div class="text-center flex-1">
            <div class="text-gray-600 text-sm mb-1">累计交易量</div>
            <div class="text-2xl font-bold text-blue-600">* * * *</div>
            <div class="text-xs text-gray-500">吨</div>
          </div>
        </div>

        <!-- 统计指标 -->
        <div class="border-t border-gray-100 pt-3">
          <div class="grid grid-cols-4 gap-2">
            <div class="text-center">
              <div class="text-base font-bold text-blue-600">* * *</div>
              <div class="text-xs text-gray-500 mt-1">用户数量</div>
            </div>
            <div class="text-center">
              <div class="text-base font-bold text-green-600">* * *</div>
              <div class="text-xs text-gray-500 mt-1">昨日收益</div>
            </div>
            <div class="text-center">
              <div class="text-base font-bold text-purple-600">* * *</div>
              <div class="text-xs text-gray-500 mt-1">累计收益</div>
            </div>
            <div class="text-center">
              <div class="text-base font-bold text-orange-600">* * *</div>
              <div class="text-xs text-gray-500 mt-1">基地资源</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计数字卡片 -->
      <!-- <div class="px-5 -mt-10">
        <div class="bg-white rounded-lg shadow grid grid-cols-4 divide-x divide-gray-100">
          <div class="p-3 text-center">
            <div class="text-blue-600 font-bold">11224</div>
            <div class="text-xs text-gray-500 mt-1">订单数</div>
          </div>
          <div class="p-3 text-center">
            <div class="text-green-600 font-bold">122</div>
            <div class="text-xs text-gray-500 mt-1">品种数</div>
          </div>
          <div class="p-3 text-center">
            <div class="text-purple-600 font-bold">4817</div>
            <div class="text-xs text-gray-500 mt-1">用户数</div>
          </div>
          <div class="p-3 text-center">
            <div class="text-orange-600 font-bold">71561</div>
            <div class="text-xs text-gray-500 mt-1">资源亩</div>
          </div>
        </div>
      </div> -->

      <!-- 最新动态 -->
      <div class="mt-4">
        <div class="mb-3">
          <div class="text-base font-medium text-gray-800">最新动态</div>
        </div>

        <div class="space-y-2">
          <div v-for="(item, index) in tradeList" :key="index" 
               class="bg-white rounded-lg p-3 shadow-sm border-l-3" 
               :style="{ borderLeftColor: getTradeBorderColor(index) }">
            <div class="flex justify-between items-center">
              <div class="flex-1">
                <div class="font-medium text-gray-800 text-sm">{{ item.name }} ({{ item.spec }})</div>
                <div class="text-xs text-gray-500 mt-1">{{ item.buyer || '采购商:' + item.name.charAt(0) + '****' }}</div>
              </div>
              <div class="text-right ml-3">
                <div class="text-base font-bold" :style="{ color: getTradeBorderColor(index) }">
                  {{ item.amount }}万
                </div>
                <div class="text-xs text-gray-400">{{ item.date }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  const tradeList = ref([
    { name: '三七', spec: '本店剪口', buyer: '采购商: 用******', amount: '64.20', date: '06-29 14:33' },
    { name: '桉叶油', spec: '毛油', buyer: '供应商: ********公司', amount: '10.48', date: '06-26 11:09' },
    { name: '木香', spec: '统货', buyer: '采购商: 用******', amount: '0.70', date: '06-28 10:54' },
    { name: '白及', spec: '统货个子', buyer: '采购商: 用******', amount: '0.01', date: '06-30 09:15' },
    { name: '天麻', spec: '特级', buyer: '采购商: 用******', amount: '15.30', date: '06-25 16:42' },
  ]);

  const borderColors = ['#4169E1', '#50C878', '#FF7F50', '#9370DB', '#FF6347'];
  const getTradeBorderColor = (index: number) => {
    return borderColors[index % borderColors.length];
  };
</script>

<style lang="scss" scoped>
  :deep(.uni-navbar-btn-text text) {
    font-size: 32rpx !important;
    font-weight: bolder !important;
    margin-left: 12rpx !important;
  }
</style>
