<template>
  <ly-layout>
    <!-- 历史记录列表 -->
    <div v-if="historyData.length > 0" class="px-4 py-4">
      <!-- 统计信息 -->
      <div class="bg-gradient-to-r from-theme-blue to-blue-500 rounded-lg p-4 mb-4 text-white">
        <div class="flex items-center justify-between mb-2">
          <div class="text-sm opacity-90">历史记录统计</div>
          <div class="text-xs opacity-75">共 {{ historyData.length }} 条记录</div>
        </div>
        <div class="grid grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-lg font-bold">{{ maxPrice }}</div>
            <div class="text-xs opacity-75">最高价</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold">{{ minPrice }}</div>
            <div class="text-xs opacity-75">最低价</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold">{{ avgPrice }}</div>
            <div class="text-xs opacity-75">平均价</div>
          </div>
        </div>
      </div>

      <!-- 历史记录列表 -->
      <div class="space-y-3">
        <div v-for="(item, index) in historyData" :key="index" class="bg-white rounded-lg border border-gray-100 p-4 shadow-sm">
          <div class="flex items-center justify-between">
            <!-- 左侧信息 -->
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-1">
                <div class="text-sm font-medium text-gray-800">
                  {{ monkey.$dayjs(item.jgtjrq).format('YYYY年MM月DD日') }}
                </div>
                <!-- <div class="text-xs text-gray-500">
                  {{ monkey.$dayjs(item.jgtjrq).format('dddd') }}
                </div> -->
              </div>
              <!-- <div class="text-xs text-gray-500">
                {{ monkey.$dayjs(item.jgtjrq).format('YYYY-MM-DD') }}
              </div> -->
            </div>

            <!-- 右侧价格 -->
            <div class="text-right">
              <div class="text-lg font-bold text-gray-800">
                <ly-price-format :price="item.jg" />
              </div>
              <!-- <div v-if="index < historyData.length - 1" class="text-xs" :class="getPriceChangeClass(item, historyData[index + 1])">
                {{ getPriceChange(item, historyData[index + 1]) }}
              </div> -->
            </div>
          </div>

          <!-- 价格变化趋势指示器
          <div v-if="index < historyData.length - 1" class="mt-3 pt-3 border-t border-gray-50">
            <div class="flex items-center gap-2">
              <div class="flex-1 h-1 bg-gray-100 rounded-full overflow-hidden">
                <div
                  class="h-full transition-all duration-300 rounded-full"
                  :class="
                    getPriceChangeClass(item, historyData[index + 1]).includes('text-green')
                      ? 'bg-green-500'
                      : getPriceChangeClass(item, historyData[index + 1]).includes('text-red')
                        ? 'bg-red-500'
                        : 'bg-gray-400'
                  "
                  :style="{ width: Math.abs(getPriceChangePercent(item, historyData[index + 1])) * 10 + '%' }"
                ></div>
              </div>
              <div class="text-xs text-gray-500">
                {{ getPriceChangePercent(item, historyData[index + 1]) > 0 ? '上涨' : getPriceChangePercent(item, historyData[index + 1]) < 0 ? '下跌' : '持平' }}
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="flex flex-col items-center justify-center py-20 px-4">
      <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <text class="i-mdi-chart-line text-2xl text-gray-400"></text>
      </div>
      <div class="text-gray-500 text-center">
        <div class="text-base font-medium mb-1">暂无历史记录</div>
        <div class="text-sm">该药材暂时没有价格历史数据</div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  import { MedicinalItem } from '@/monkey/types';

  const ycbm = ref('');
  const historyData = ref<MedicinalPrice[]>([]);
  const medicineInfo = ref({
    ycmc: '',
  });

  // 计算统计数据
  const maxPrice = computed(() => {
    if (historyData.value.length === 0) return '0.00';
    const prices = historyData.value.map((item) => Number(item.jg || 0));
    return Math.max(...prices).toFixed(2);
  });

  const minPrice = computed(() => {
    if (historyData.value.length === 0) return '0.00';
    const prices = historyData.value.map((item) => Number(item.jg || 0));
    return Math.min(...prices).toFixed(2);
  });

  const avgPrice = computed(() => {
    if (historyData.value.length === 0) return '0.00';
    const prices = historyData.value.map((item) => Number(item.jg || 0));
    const sum = prices.reduce((a, b) => a + b, 0);
    return (sum / prices.length).toFixed(2);
  });

  // 获取价格变化百分比
  const getPriceChangePercent = (current, previous) => {
    const currentPrice = Number(current.jg || 0);
    const previousPrice = Number(previous.jg || 0);

    if (previousPrice === 0) return 0;

    return ((currentPrice - previousPrice) / previousPrice) * 100;
  };

  // 获取价格变化文本
  const getPriceChange = (current, previous) => {
    const percent = getPriceChangePercent(current, previous);

    if (percent === 0) return '持平';

    const sign = percent > 0 ? '+' : '';
    return `${sign}${percent.toFixed(2)}%`;
  };

  // 获取价格变化样式类
  const getPriceChangeClass = (current, previous) => {
    const percent = getPriceChangePercent(current, previous);

    if (percent > 0) return 'text-green-600';
    if (percent < 0) return 'text-red-600';
    return 'text-gray-500';
  };

  // 获取历史数据
  const getHistory = async () => {
    if (!ycbm.value) return;

    try {
      const { errcode, data } = await monkey.$api.medicine.getMedicinalItemDetail(ycbm.value);
      if (errcode === 0) {
        // 按日期排序，最新的在前面
        historyData.value = (data.medicinalPriceList || []).sort((a, b) => monkey.$dayjs(b.jgtjrq).valueOf() - monkey.$dayjs(a.jgtjrq).valueOf());

        // 设置药材信息
        if (data.medicinalPriceToday) medicineInfo.value.ycmc = data.medicinalPriceToday.ycmc || '';
      }
    } catch (error) {
      console.log('🚀 ~ getHistory ~ error:', error);
      monkey.$toast.error('获取药材价格历史失败');
    }
  };

  onLoad((options) => {
    ycbm.value = options.ycbm || '';
    if (ycbm.value) {
      getHistory();
    }
  });
</script>
