<template>
  <ly-layout isArcBg>
    <div class="relative z-10 pb-32">
      <div class="px-4 space-y-3 mt-3">
        <!-- 货物主要信息卡片 -->
        <div :class="['rounded-lg shadow-md overflow-hidden', goodsDetail.status === 'pledged' ? 'bg-gray-50' : 'bg-white']">
          <!-- 货物图片展示 -->
          <div class="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200">
            <img v-if="goodsDetail.image" :src="goodsDetail.image" :alt="goodsDetail.name" class="w-full h-full object-cover" @error="handleImageError" />
            <div v-else class="flex items-center justify-center h-full">
              <i class="i-line-md-image text-4xl text-gray-400"></i>
            </div>
            <!-- 状态标签 -->
            <div class="absolute top-4 right-4">
              <div
                :class="[
                  'px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm',
                  goodsDetail.status === 'normal'
                    ? 'bg-theme-green/20 text-theme-green'
                    : goodsDetail.status === 'expiring'
                      ? 'bg-yellow-500/20 text-yellow-700'
                      : goodsDetail.status === 'expired'
                        ? 'bg-red-500/20 text-red-700'
                        : 'bg-gray-400/20 text-gray-600',
                ]"
              >
                {{ getStatusText(goodsDetail.status) }}
              </div>
            </div>
          </div>

          <!-- 货物名称和基本信息 -->
          <div class="p-4">
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1">
                <div class="text-xl font-bold text-gray-800 mb-2 leading-tight">{{ goodsDetail.name }}</div>
                <div class="flex items-center text-gray-600">
                  <i class="i-line-md-hash-small text-base mr-1"></i>
                  <div class="text-sm">编号: {{ goodsDetail.id }}</div>
                </div>
              </div>
            </div>

            <!-- 核心数据展示 -->
            <div class="grid grid-cols-2 gap-3 mb-5">
              <div class="bg-gradient-to-r from-theme-blue/10 to-theme-blue/5 rounded-xl p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm text-gray-600 mb-1">库存数量</div>
                    <div class="text-xl font-bold text-theme-blue leading-tight">{{ goodsDetail.quantity }}</div>
                    <div class="text-xs text-gray-500 mt-1">{{ goodsDetail.unit }}</div>
                  </div>
                  <div class="w-10 h-10 bg-theme-blue/20 rounded-full flex items-center justify-center">
                    <i class="i-line-md-clipboard-list text-theme-blue text-lg"></i>
                  </div>
                </div>
              </div>

              <div class="bg-gradient-to-r from-theme-green/10 to-theme-green/5 rounded-xl p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm text-gray-600 mb-1">总重量</div>
                    <div class="text-xl font-bold text-theme-green leading-tight">{{ goodsDetail.weight }}</div>
                    <div class="text-xs text-gray-500 mt-1">kg</div>
                  </div>
                  <div class="w-10 h-10 bg-theme-green/20 rounded-full flex items-center justify-center">
                    <i class="i-line-md-gauge text-theme-green text-lg"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 仓储信息卡片 -->
        <div class="bg-white rounded-lg shadow-md p-4">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
              <i class="i-mdi-cube text-purple-600 text-lg"></i>
            </div>
            <div class="text-base font-semibold text-gray-800 leading-tight">仓储信息</div>
          </div>

          <div class="grid grid-cols-1 gap-3">
            <div class="flex items-center justify-between py-3 border-b border-gray-100">
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                <div class="text-sm text-gray-600">仓库位置</div>
              </div>
              <div class="font-medium text-sm text-gray-800">{{ goodsDetail.warehouse }}</div>
            </div>

            <div class="flex items-center justify-between py-3 border-b border-gray-100">
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div class="text-sm text-gray-600">货位编号</div>
              </div>
              <div class="font-medium text-sm text-gray-800">{{ goodsDetail.location }}</div>
            </div>

            <div class="flex items-center justify-between py-3">
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <div class="text-sm text-gray-600">批次号</div>
              </div>
              <div class="font-medium text-sm text-gray-800">{{ goodsDetail.batchNo || '暂无' }}</div>
            </div>
          </div>
        </div>

        <!-- 时间信息卡片 -->
        <div class="bg-white rounded-lg shadow-md p-4">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
              <i class="i-line-md-calendar text-orange-600 text-lg"></i>
            </div>
            <div class="text-base font-semibold text-gray-800 leading-tight">时间信息</div>
          </div>

          <div class="grid grid-cols-1 gap-3">
            <div class="bg-gray-50 rounded-xl p-4">
              <div class="flex items-center justify-between mb-2">
                <div class="text-sm text-gray-600">存入时间</div>
                <div class="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">已入库</div>
              </div>
              <div class="flex items-center space-x-2">
                <i class="i-line-md-clock text-gray-500"></i>
                <div class="font-medium text-sm text-gray-800">{{ goodsDetail.storageDate }}</div>
              </div>
            </div>

            <div class="bg-gray-50 rounded-xl p-4">
              <div class="flex items-center justify-between mb-2">
                <div class="text-sm text-gray-600">到期时间</div>
                <div :class="['text-xs px-2 py-1 rounded-full', isExpiringSoon(goodsDetail.expiryDate) ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600']">
                  {{ isExpiringSoon(goodsDetail.expiryDate) ? '即将到期' : '正常' }}
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <i class="i-line-md-calendar-check text-gray-500"></i>
                <div :class="['font-medium text-sm', isExpiringSoon(goodsDetail.expiryDate) ? 'text-red-600' : 'text-gray-800']">
                  {{ goodsDetail.expiryDate }}
                </div>
              </div>
              <div class="mt-2 text-xs text-gray-500">剩余 {{ getRemainingDays(goodsDetail.expiryDate) }} 天</div>
            </div>
          </div>

          <!-- 质押状态说明 -->
          <div v-if="goodsDetail.status === 'pledged'" class="mt-4 p-3 bg-gray-100 border border-gray-300 rounded-lg">
            <div class="flex items-center space-x-2">
              <i class="i-line-md-alert text-gray-600"></i>
              <div class="text-sm text-gray-700 font-medium">质押状态</div>
            </div>
            <div class="text-xs text-gray-600 mt-1">该货物已进入质押状态，库存已冻结，暂时无法进行出库操作。</div>
          </div>
        </div>

        <!-- 操作历史记录 -->
        <div class="bg-white rounded-lg shadow-md p-4">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                <i class="i-line-md-list text-indigo-600 text-lg"></i>
              </div>
              <div class="text-base font-semibold text-gray-800 leading-tight">操作记录</div>
            </div>
            <div @click="viewAllHistory" class="text-sm text-theme-blue hover:text-theme-blue-600">查看全部</div>
          </div>

          <div class="space-y-3">
            <div v-for="(record, index) in operationHistory" :key="index" class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm">
                <i :class="record.icon" class="text-sm"></i>
              </div>
              <div class="flex-1">
                <div class="text-sm font-medium text-gray-800 leading-tight">{{ record.action }}</div>
                <div class="text-xs text-gray-500 mt-1">{{ record.time }}</div>
              </div>
              <div class="text-xs text-gray-400">
                {{ record.operator }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <ly-fixed-btns>
        <div class="px-4">
          <div class="flex gap-3 justify-end">
            <div
              @click="handleViewGoods"
              class="flex items-center justify-center bg-theme-blue text-white text-sm tracking-wider py-2 px-5 rounded-full active:bg-theme-blue-600 transition-all duration-200 active:scale-95"
            >
              <i class="i-line-md-watch-twotone-loop mr-2"></i>
              <div>看货</div>
            </div>
            <div
              v-if="goodsDetail.status !== 'pledged'"
              @click="handleOutbound"
              class="flex items-center justify-center bg-theme-green text-white text-xs tracking-wider py-2 px-5 rounded-full active:bg-theme-green-600 transition-all duration-200 active:scale-95"
            >
              <i class="i-line-md-upload mr-2"></i>
              <div>出库</div>
            </div>
            <div
              v-if="goodsDetail.status !== 'pledged'"
              @click="handlePledge"
              class="flex items-center justify-center bg-gray-600 text-white text-sm tracking-wider py-2 px-5 rounded-full active:bg-gray-700 transition-all duration-200 active:scale-95"
            >
              <i class="i-line-md-document mr-2"></i>
              <div>质押</div>
            </div>
          </div>
        </div>
      </ly-fixed-btns>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  interface GoodsDetail {
    id: string;
    name: string;
    quantity: number;
    unit: string;
    weight: number;
    warehouse: string;
    location: string;
    storageDate: string;
    expiryDate: string;
    status: 'normal' | 'expiring' | 'expired' | 'pledged';
    batchNo?: string;
    image?: string;
  }

  interface OperationRecord {
    action: string;
    time: string;
    operator: string;
    icon: string;
  }

  const goodsDetail = ref<GoodsDetail>({
    id: 'WH001',
    name: '当归',
    quantity: 500,
    unit: 'kg',
    weight: 500,
    warehouse: 'A区仓库',
    location: 'A-01-001',
    storageDate: '2024-01-15',
    expiryDate: '2025-01-15',
    status: 'normal',
    batchNo: 'DG20240115001',
    image: 'https://picsum.photos/200/300',
  });

  const operationHistory = ref<OperationRecord[]>([
    {
      action: '货物入库',
      time: '2024-01-15 10:30',
      operator: '张三',
      icon: 'i-line-md-download text-green-600',
    },
    {
      action: '质量检查',
      time: '2024-01-15 14:20',
      operator: '李四',
      icon: 'i-line-md-clipboard-check text-blue-600',
    },
    {
      action: '仓位调整',
      time: '2024-01-16 09:15',
      operator: '王五',
      icon: 'i-line-md-navigation text-purple-600',
    },
  ]);

  const getStatusText = (status: string) => {
    switch (status) {
      case 'normal':
        return '正常';
      case 'expiring':
        return '即将到期';
      case 'expired':
        return '已过期';
      case 'pledged':
        return '已质押';
      default:
        return '未知';
    }
  };

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30;
  };

  const getRemainingDays = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  const handleImageError = () => {
    goodsDetail.value.image = '';
  };

  const goBack = () => {
    uni.navigateBack();
  };

  const shareGoods = () => {
    monkey.$helper.toast.success('分享功能开发中');
  };

  const editGoods = () => {
    monkey.$helper.toast.success('编辑功能开发中');
  };

  const handleViewGoods = () => {
    monkey.$helper.toast.success('申请看货');
  };

  const handleOutbound = () => {
    monkey.$helper.toast.success('申请出库');
  };

  const handlePledge = () => {
    monkey.$helper.toast.success('申请质押');
  };

  const viewAllHistory = () => {
    monkey.$helper.toast.success('查看完整操作记录');
  };

  onLoad((options: any) => {
    if (options.id) {
      // 根据ID加载具体货物信息
      console.log('加载货物详情:', options.id);
    }
  });
</script>

<style lang="scss" scoped>
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }
</style>
