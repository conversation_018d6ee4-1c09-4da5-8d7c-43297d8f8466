<template>
  <ly-layout>
    <!-- 运输队列表 -->
    <div class="px-4 py-4">
      <div class="space-y-3">
        <div v-for="(item, index) in transportTeams" :key="index" class="bg-white rounded-lg shadow-md p-4 active:shadow-lg transition-all duration-300">
          <div class="flex items-center gap-4">
            <!-- 运输队头像 -->
            <div class="flex-shrink-0">
              <image :src="item.avatar" class="w-16 h-16 rounded-xl object-cover border-2 border-blue-100 shadow-sm" mode="aspectFill" />
            </div>

            <!-- 运输队信息 -->
            <div class="flex-1 min-w-0">
              <div class="font-semibold text-gray-900 text-lg mb-2 truncate">
                {{ item.teamName }}
              </div>
              <div class="space-y-1">
                <div class="flex items-center gap-2 text-sm text-gray-600">
                  <i class="i-mdi-account text-blue-500 text-base" />
                  <text>{{ item.contactPerson }}</text>
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                  <i class="i-mdi-phone text-green-500 text-base" />
                  <text>{{ item.contactPhone }}</text>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="flex items-center gap-3 mt-4 pt-3 border-t border-gray-100">
            <!-- 查看详情按钮 -->
            <div
              class="flex-1 flex items-center justify-center gap-2 py-2.5 px-4 bg-blue-50 text-blue-600 rounded-lg border border-blue-200 active:bg-blue-100 transition-colors duration-200"
              @click.stop="viewDetails(item)"
            >
              <i class="i-mdi-information-outline text-base" />
              <text class="text-xs font-medium">查看详情</text>
            </div>

            <!-- 拨打电话按钮 -->
            <div
              class="flex-1 flex items-center justify-center gap-2 py-2.5 px-4 bg-green-50 text-green-600 rounded-lg border border-green-200 active:bg-green-100 transition-colors duration-200"
              @click.stop="makePhoneCall(item.contactPhone)"
            >
              <i class="i-mdi-phone text-base" />
              <text class="text-xs font-medium">立即联系</text>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="transportTeams.length === 0" class="flex flex-col items-center justify-center py-20">
      <div class="text-gray-300 text-6xl mb-4">
        <i class="i-mdi-truck" />
      </div>
      <div class="text-gray-400 text-base">暂无运输队信息</div>
    </div>
  </ly-layout>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import monkey from '@/monkey';

  interface TransportTeam {
    id: string;
    teamName: string;
    contactPerson: string;
    contactPhone: string;
    avatar: string;
  }

  // 运输队列表
  const transportTeams = ref<TransportTeam[]>([
    {
      id: '1',
      teamName: '快捷物流运输队',
      contactPerson: '张师傅',
      contactPhone: '13800138001',
      avatar: 'https://picsum.photos/120/120?random=1',
    },
    {
      id: '2',
      teamName: '顺风货运',
      contactPerson: '李经理',
      contactPhone: '13800138002',
      avatar: 'https://picsum.photos/120/120?random=2',
    },
    {
      id: '3',
      teamName: '安全运输有限公司',
      contactPerson: '王队长',
      contactPhone: '13800138003',
      avatar: 'https://picsum.photos/120/120?random=3',
    },
    {
      id: '4',
      teamName: '城际专线物流',
      contactPerson: '赵师傅',
      contactPhone: '13800138004',
      avatar: 'https://picsum.photos/120/120?random=4',
    },
    {
      id: '5',
      teamName: '金牌货运队',
      contactPerson: '刘总',
      contactPhone: '13800138005',
      avatar: 'https://picsum.photos/120/120?random=5',
    },
  ]);

  // 查看详情
  const viewDetails = (team: TransportTeam) => {
    console.log('查看运输队详情:', team);
    monkey.$helper.toast.success(`查看 ${team.teamName} 详情`);
    // TODO: 跳转到详情页面或显示详情弹窗
    // uni.navigateTo({
    //   url: `/pages/transport-detail?id=${team.id}`
    // });
  };

  // 拨打电话
  const makePhoneCall = (phoneNumber: string) => {
    uni.makePhoneCall({
      phoneNumber,
      success: () => {
        console.log('拨打电话成功');
        monkey.$helper.toast.success('正在拨打电话...');
      },
      fail: (err) => {
        console.error('拨打电话失败', err);
        monkey.$helper.toast.error('拨打电话失败');
      },
    });
  };
</script>
