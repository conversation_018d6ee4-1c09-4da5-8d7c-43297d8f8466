<template>
  <ly-layout>
    <div class="py-2 sticky top-0 bg-white z-10 border-b border-gray-100 shadow-sm">
      <uni-segmented-control :current="current" styleType="text" :values="items" class="font-medium"></uni-segmented-control>
    </div>
    <div class="p-4 space-y-4">
      <div v-for="order in mockSellOrders" :key="order.id" class="bg-white border-0 shadow-sm rounded-lg active:shadow-md transition-all duration-200" @click="monkey.$router.navigateTo(`/modules/order/sell/detail?id=${order.id}`)">
        <div class="p-4">
          <!-- Order Header -->
          <div class="flex items-center justify-between mb-4 pb-3 border-b border-gray-100">
            <div class="flex items-center gap-2">
              <div class="w-9 h-9 rounded-full bg-blue-50 shadow-sm flex items-center justify-center">
                <text class="i-mdi-account-file-text text-xl text-blue-500"></text>
              </div>
              <div>
                <text class="font-medium text-sm flex items-center gap-2">
                  <text class="i-mdi-account-outline text-base text-gray-600"></text>
                  {{ order.buyerName }}
                </text>
                <text class="text-xs text-gray-500">订单号: {{ order.orderNumber }}</text>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <div v-if="order.urgentLevel" class="px-2 py-1 rounded-full text-xs font-medium border-0" :class="getUrgentBadge(order.urgentLevel).color">
                {{ getUrgentBadge(order.urgentLevel).label }}
              </div>
              <div class="px-3 py-1 rounded-full text-xs border-0 font-medium flex items-center gap-1" :class="getStatusBadge(order.status).color">
                {{ getStatusBadge(order.status).label }}
              </div>
            </div>
          </div>

          <!-- Product Info -->
          <div class="flex gap-3 mb-4 p-2 bg-gray-50 rounded-lg">
            <img :src="order.image" class="w-16 h-16 rounded-lg object-cover bg-white shadow-sm" />
            <div class="flex-1">
              <div class="font-medium text-sm mb-1 line-clamp-2 text-gray-800">{{ order.title }}</div>
              <div class="text-xs mb-2 text-gray-600">{{ order.specs }}</div>
              <div class="flex items-center justify-between mb-3">
                <text class="text-xs bg-white px-2 py-0.5 rounded-full text-gray-500 shadow-sm">数量: {{ order.quantity }}</text>
                <div class="text-right flex-1 flex flex-col items-end">
                  <div class="text-xs text-gray-500">单价: <text class="text-[10px] mr-0.5">¥</text>{{ order.price }}</div>
                </div>
              </div>
              <div class="flex-1 flex flex-col items-end">
                <div class="text-sm font-medium text-green-600">总价：<text class="text-[10px] mr-0.5">¥</text>{{ order.totalAmount }}</div>
              </div>
            </div>
          </div>

          <!-- Profit Info -->
          <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-3 mb-4 shadow-sm border border-green-100">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <div class="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center">
                  <text class="i-mdi-database-clock-outline text-base text-green-600"></text>
                </div>
                <text class="text-sm font-medium text-green-800">预计利润</text>
              </div>
              <div :class="order.profit >= 0 ? 'text-green-600' : 'text-red-600'" class="font-bold"><text class="text-[10px] mr-0.5">¥</text>{{ order.profit.toLocaleString() }}</div>
            </div>
            <text class="text-xs text-green-600 mt-2 bg-white px-2 py-0.5 rounded-full inline-block shadow-sm">利润率: {{ ((order.profit / order.totalAmount) * 100).toFixed(1) }}%</text>
          </div>

          <!-- Order Details -->
          <div class="bg-gray-50 rounded-lg p-3 mb-4 space-y-2 border border-gray-100">
            <div class="flex items-center gap-2 text-xs text-gray-600">
              <text class="i-mdi-calendar-clock-outline text-lg text-blue-500"></text>
              <text>下单时间: {{ order.orderTime }}</text>
            </div>
            <div v-if="order.paymentTime" class="flex items-center gap-2 text-xs text-gray-600">
              <text class="i-mdi-book-clock-outline text-lg text-green-500"></text>
              <text>付款时间: {{ order.paymentTime }}</text>
            </div>
            <div v-if="order.deliveryTime" class="flex items-center gap-2 text-xs text-gray-600">
              <text class="i-mdi-clipboard-text-clock-outline text-lg text-purple-500"></text>
              <text>发货时间: {{ order.deliveryTime }}</text>
            </div>
            <div v-if="order.completedTime" class="flex items-center gap-2 text-xs text-gray-600">
              <text class="i-mdi-calendar-check-outline text-lg text-emerald-500"></text>
              <text>完成时间: {{ order.completedTime }}</text>
            </div>
            <div class="flex items-center gap-2 text-xs text-gray-600">
              <text class="i-mdi-archive-marker-outline text-lg text-orange-500"></text>
              <text>收货地址: {{ order.deliveryAddress }}</text>
            </div>
            <div class="flex items-center gap-2 text-xs text-gray-600">
              <text class="i-mdi-phone-settings-outline text-lg text-indigo-500"></text>
              <text>买家电话: {{ order.buyerPhone }}</text>
            </div>
            <div v-if="order.courierCompany && order.trackingNumber" class="flex items-center gap-2 text-xs text-gray-600">
              <text class="i-mdi-truck-outline text-lg text-cyan-500"></text>
              <text>物流信息: {{ order.courierCompany }} {{ order.trackingNumber }}</text>
            </div>
          </div>

          <!-- Rating Display for Completed Orders -->
          <div v-if="order.status === 'completed' && order.rating" class="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-3 mb-4 border border-yellow-100">
            <div class="flex items-center gap-2 mb-2">
              <text class="i-mdi-star text-lg text-yellow-500"></text>
              <text class="text-sm font-medium text-gray-700">买家评价:</text>
              <div class="flex items-center gap-1">
                <text v-for="i in 5" :key="i" class="i-mdi-star-outline text-base" :class="i <= order.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'" />
                <text class="text-xs text-gray-500 ml-1">({{ order.rating }}分)</text>
              </div>
            </div>
            <text v-if="order.ratingComment" class="text-xs text-gray-600 bg-white rounded-lg p-2 block shadow-sm border border-yellow-50">"{{ order.ratingComment }}"</text>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end pt-2">
            <div class="flex gap-2">
              <template v-if="order.status === 'pending'">
                <div @click="contactBuyer(order)" class="px-3 py-2 text-xs text-gray-600 border border-gray-300 rounded-lg bg-white active:bg-gray-50 transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-phone-forward-outline text-base"></text>
                  联系买家
                </div>
                <div @click="confirmOrder(order)" class="px-3 py-2 text-xs bg-gradient-to-r from-blue-500 to-blue-600 active:from-blue-600 active:to-blue-700 text-white rounded-lg transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-check-circle-outline text-base"></text>
                  确认订单
                </div>
                <div @click="rejectOrder(order)" class="px-3 py-2 text-xs text-red-600 border border-red-200 bg-white active:bg-red-50 rounded-lg transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-close-circle-outline text-base"></text>
                  拒绝订单
                </div>
              </template>

              <template v-else-if="order.status === 'payment'">
                <div @click="contactBuyer(order)" class="px-3 py-2 text-xs text-gray-600 border border-gray-300 rounded-lg bg-white active:bg-gray-50 transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-phone-forward-outline text-base"></text>
                  联系买家
                </div>
                <div @click="editPrice(order)" class="px-3 py-2 text-xs text-green-600 border border-green-300 rounded-lg bg-white active:bg-green-50 transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-pencil-outline text-base"></text>
                  修改价格
                </div>
              </template>

              <template v-else-if="order.status === 'delivery'">
                <div @click="viewDetails(order)" class="px-3 py-2 text-xs text-gray-600 border border-gray-300 rounded-lg bg-white active:bg-gray-50 transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-eye-outline text-base"></text>
                  查看详情
                </div>
                <div @click="arrangeDelivery(order)" class="px-3 py-2 text-xs bg-gradient-to-r from-purple-500 to-purple-600 active:from-purple-600 active:to-purple-700 text-white rounded-lg transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-checkbox-marked-circle-auto-outline text-base"></text>
                  安排发货
                </div>
              </template>

              <template v-else-if="order.status === 'completed'">
                <div @click="contactBuyer(order)" class="px-3 py-2 text-xs text-gray-600 border border-gray-300 rounded-lg bg-white active:bg-gray-50 transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-phone-forward-outline text-base"></text>
                  联系买家
                </div>
                <div @click="viewAnalytics(order)" class="px-3 py-2 text-xs text-gray-600 border border-gray-300 rounded-lg bg-white active:bg-gray-50 transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-chart-box-outline text-base"></text>
                  查看数据
                </div>
              </template>

              <template v-else-if="order.status === 'refund'">
                <div @click="contactBuyer(order)" class="px-3 py-2 text-xs text-gray-600 border border-gray-300 rounded-lg bg-white active:bg-gray-50 transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-phone-forward-outline text-base"></text>
                  联系买家
                </div>
                <div @click="handleRefund(order)" class="px-3 py-2 text-xs bg-gradient-to-r from-orange-500 to-orange-600 active:from-orange-600 active:to-orange-700 text-white rounded-lg transition-colors flex items-center gap-1 shadow-sm">
                  <text class="i-mdi-invoice-import-outline text-base"></text>
                  处理退款
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
import monkey from '@/monkey'
  type OrderStatus = 'all' | 'pending' | 'payment' | 'shipping' | 'completed' | 'cancelled';

  const items = ['全部', '待确认', '待收款', '待发货', '已完成', '已取消'];

  const current = ref(0);

  interface Order {
    id: string;
    orderNumber: string;
    status: OrderStatus;
    title: string;
    specs: string;
    quantity: number;
    price: number;
    totalAmount: number;
    shopName: string;
    shopAvatar: string;
    orderTime: string;
    paymentTime?: string;
    shippingTime?: string;
    completedTime?: string;
    address: string;
    phone: string;
    image: string;
    rating?: number;
  }

  // 模拟订单数据
  const mockSellOrders = reactive([
    {
      id: '1',
      orderNumber: 'S202412010001',
      status: 'pending',
      title: '高品质不锈钢管材',
      specs: '规格: 304不锈钢 直径25mm',
      quantity: 100,
      price: 45.8,
      totalAmount: 4580,
      profit: 1374,
      buyerName: '张先生',
      buyerAvatar: '/placeholder.svg?height=40&width=40',
      buyerPhone: '138****8888',
      orderTime: '2024-12-01 14:30',
      deliveryAddress: '广东省深圳市南山区科技园南区',
      image: '/placeholder.svg?height=80&width=80',
      urgentLevel: 'urgent',
    },
    {
      id: '2',
      orderNumber: 'S202411280002',
      status: 'payment',
      title: '工业级铝合金型材',
      specs: '规格: 6061铝合金 长度3米',
      quantity: 50,
      price: 128.5,
      totalAmount: 6425,
      profit: 1927.5,
      buyerName: '李经理',
      buyerAvatar: '/placeholder.svg?height=40&width=40',
      buyerPhone: '139****9999',
      orderTime: '2024-11-28 09:15',
      paymentTime: '2024-11-28 09:20',
      deliveryAddress: '江苏省苏州市工业园区星海街',
      image: '/placeholder.svg?height=80&width=80',
      urgentLevel: 'very_urgent',
    },
    {
      id: '3',
      orderNumber: 'S202411250003',
      status: 'delivery',
      title: '精密机械零配件',
      specs: '规格: 标准件 M8螺栓',
      quantity: 1000,
      price: 2.5,
      totalAmount: 2500,
      profit: 750,
      buyerName: '王总',
      buyerAvatar: '/placeholder.svg?height=40&width=40',
      buyerPhone: '137****7777',
      orderTime: '2024-11-25 16:20',
      paymentTime: '2024-11-25 16:25',
      deliveryTime: '2024-11-26 10:30',
      deliveryAddress: '浙江省杭州市余杭区文一西路',
      courierCompany: '顺丰速运',
      trackingNumber: 'SF1234567890',
      image: '/placeholder.svg?height=80&width=80',
    },
    {
      id: '4',
      orderNumber: 'S202411200004',
      status: 'completed',
      title: '电子元器件套装',
      specs: '规格: 电阻电容混装',
      quantity: 1,
      price: 299,
      totalAmount: 299,
      profit: 119.6,
      buyerName: '陈工程师',
      buyerAvatar: '/placeholder.svg?height=40&width=40',
      buyerPhone: '136****6666',
      orderTime: '2024-11-20 11:45',
      paymentTime: '2024-11-20 11:50',
      deliveryTime: '2024-11-21 08:00',
      completedTime: '2024-11-23 14:30',
      deliveryAddress: '上海市浦东新区张江高科技园区',
      courierCompany: '中通快递',
      trackingNumber: 'ZTO9876543210',
      image: '/placeholder.svg?height=80&width=80',
      rating: 5,
      ratingComment: '质量很好，发货速度快，服务态度佳！',
    },
    {
      id: '5',
      orderNumber: 'S202411150005',
      status: 'refund',
      title: '化工原料批发',
      specs: '规格: 工业级纯度99%',
      quantity: 25,
      price: 180,
      totalAmount: 4500,
      profit: -4500,
      buyerName: '刘总监',
      buyerAvatar: '/placeholder.svg?height=40&width=40',
      buyerPhone: '135****5555',
      orderTime: '2024-11-15 13:20',
      paymentTime: '2024-11-15 13:25',
      deliveryAddress: '山东省青岛市黄岛区经济开发区',
      image: '/placeholder.svg?height=80&width=80',
    },
  ]);

  // 订单状态配置
  const statusConfig = reactive({
    all: { label: '全部', color: 'bg-gray-100 text-gray-700', icon: null },
    pending: { label: '待确认', color: 'bg-blue-100 text-blue-700', icon: null },
    payment: { label: '待收款', color: 'bg-green-100 text-green-700', icon: null },
    delivery: { label: '待发货', color: 'bg-purple-100 text-purple-700', icon: null },
    completed: { label: '已完成', color: 'bg-emerald-100 text-emerald-700', icon: null },
    cancelled: { label: '已取消', color: 'bg-red-100 text-red-700', icon: null },
    refund: { label: '退款中', color: 'bg-orange-100 text-orange-700', icon: null },
  });

  // 紧急程度配置
  const urgentLevelConfig = reactive({
    normal: { label: '普通', color: 'bg-gray-100 text-gray-600' },
    urgent: { label: '紧急', color: 'bg-yellow-100 text-yellow-700' },
    very_urgent: { label: '加急', color: 'bg-red-100 text-red-700' },
  });
  const getStatusBadge = (status) => {
    return statusConfig[status];
  };

  const getUrgentBadge = (level) => {
    if (!level || level === 'normal') return null;
    return urgentLevelConfig[level];
  };
</script>

<style lang="scss" scoped></style>
