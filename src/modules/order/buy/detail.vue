<template>
  <ly-layout>
    <div class="min-h-screen bg-gray-50 p-4 font-sans">
      <!-- 订单状态卡片 -->
      <div class="mb-4 rounded-xl bg-gradient-to-br from-blue-600 to-blue-700 p-5 text-white shadow-xl shadow-blue-500/20">
        <div class="mb-8 flex items-center justify-between">
          <div>
            <div class="text-lg font-bold">订单进行中</div>
            <div class="text-sm text-blue-200 opacity-80">状态: {{ getStatusBadge(order.status)?.label }}</div>
          </div>
          <div class="rounded-full border border-white/20 bg-white/10 px-4 py-1.5 text-xs font-semibold backdrop-blur-sm transition hover:bg-white/20">完善订单</div>
        </div>

        <!-- 订单步骤 -->
        <div class="relative">
          <!-- Progress Bar -->
          <div class="absolute top-3 left-0 w-full h-1.5">
              <div class="h-full rounded-full bg-black/20"></div>
              <div class="absolute top-0 left-0 h-full rounded-full bg-white transition-all duration-1000" :style="{width: getProgressWidth()}"></div>
          </div>

          <!-- Steps -->
          <div class="relative flex justify-between">
            <div v-for="(step, index) in orderSteps" :key="index" class="flex flex-col items-center text-center">
                <div
                    class="flex h-6 w-6 items-center justify-center rounded-full transition-all duration-500"
                    :class="[
                        isCurrentStep(index) ? 'animate-pulse-blue' : '',
                        isStepCompleted(index) || isCurrentStep(index) ? 'bg-white' : 'bg-blue-500 border-2 border-blue-400'
                    ]"
                >
                    <text v-if="isStepCompleted(index) && !isCurrentStep(index)" class="i-mdi-check font-bold text-blue-600"></text>
                    <div v-else-if="isCurrentStep(index)" class="h-2.5 w-2.5 rounded-full bg-blue-600"></div>
                </div>
              <div class="mt-3 w-16 text-xs" :class="isStepCompleted(index) || isCurrentStep(index) ? 'font-semibold text-white' : 'text-blue-200'">
                {{ step.name }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户信息卡片 -->
      <div class="group mb-4 flex cursor-pointer items-center justify-between rounded-xl bg-white p-4 shadow-sm transition-all hover:shadow-md">
        <div class="flex items-center">
          <div class="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-50">
            <text class="i-mdi-account text-2xl text-blue-500"></text>
          </div>
          <div>
            <div class="font-semibold text-gray-800">{{ order.buyerName }}</div>
            <div class="text-xs text-gray-500">订单号: {{ order.orderNumber }}</div>
          </div>
        </div>
        <text class="i-mdi-chevron-right text-gray-400 transition-transform group-hover:translate-x-1"></text>
      </div>

      <!-- 产品信息卡片 -->
      <div class="mb-4 rounded-xl bg-white p-5 shadow-sm">
        <div class="mb-4 border-b border-gray-100 pb-4">
            <div class="font-semibold text-gray-800">高品质不锈钢管材</div>
            <div class="text-sm text-gray-500">规格: 304不锈钢 直径25mm</div>
        </div>
        <div class="space-y-3 text-sm">
            <div class="flex items-center justify-between text-gray-600">
              <span>数量</span>
              <span class="font-medium text-gray-800">100</span>
            </div>
            <div class="flex items-center justify-between text-gray-600">
              <span>单价</span>
              <span class="font-medium text-gray-800">¥45.8</span>
            </div>
        </div>
        <div class="mt-4 border-t border-gray-100 pt-4 flex items-baseline justify-end space-x-2">
          <span class="text-sm text-gray-600">总价:</span>
          <span class="text-2xl font-bold text-red-500">¥4580</span>
        </div>
      </div>

      <div class="rounded-xl bg-white p-5 shadow-sm">
         <div class="space-y-4">
            <div class="flex items-center text-sm text-gray-600">
              <text class="i-mdi-clock-outline mr-4 text-xl text-gray-400"></text>
              <span class="font-medium">下单时间: {{ order.orderTime }}</span>
            </div>
            <div class="flex items-center text-sm text-gray-600">
              <text class="i-mdi-map-marker-outline mr-4 text-xl text-gray-400"></text>
              <span class="font-medium">收货地址: {{ order.deliveryAddress }}</span>
            </div>
            <div class="flex items-center text-sm text-gray-600">
              <text class="i-mdi-phone-outline mr-4 text-xl text-gray-400"></text>
              <span class="font-medium">联系电话: {{ order.buyerPhone }}</span>
            </div>
         </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">

  const orderId = '1';

  // 订单状态配置
  const statusConfig = reactive({
    all: { label: '全部', color: 'bg-gray-100 text-gray-700', icon: null },
    pending: { label: '待确认', color: 'bg-blue-100 text-blue-700', icon: null },
    payment: { label: '待收款', color: 'bg-green-100 text-green-700', icon: null },
    delivery: { label: '生产中', color: 'bg-purple-100 text-purple-700', icon: null },
    completed: { label: '已发货', color: 'bg-emerald-100 text-emerald-700', icon: null },
    cancelled: { label: '已取消', color: 'bg-red-100 text-red-700', icon: null },
    refund: { label: '退款中', color: 'bg-orange-100 text-orange-700', icon: null },
  });

  // 紧急程度配置
  const urgentLevelConfig = reactive({
    normal: { label: '普通', color: 'bg-gray-100 text-gray-600' },
    urgent: { label: '紧急', color: 'bg-yellow-100 text-yellow-700' },
    very_urgent: { label: '加急', color: 'bg-red-100 text-red-700' },
  });

  // 获取状态标签
  const getStatusBadge = (status) => {
    return statusConfig[status];
  };

  // 获取紧急程度标签
  const getUrgentBadge = (level) => {
    if (!level || level === 'normal') return null;
    return urgentLevelConfig[level];
  };

  // 订单步骤
  const orderSteps = reactive([
    { name: '已接交', status: 'pending', time: '' },
    { name: '模具认可', status: 'payment', time: '' },
    { name: '生产中', status: 'delivery', time: '' },
    { name: '已发货', status: 'completed', time: '' },
  ]);

  // 模拟订单数据
  const order = ref({
    id: '1',
    orderNumber: 'S202412010001',
    status: 'delivery', // Current status is 'delivery'
    title: '高品质不锈钢管材',
    specs: '规格: 304不锈钢 直径25mm',
    quantity: 100,
    price: 45.8,
    totalAmount: 4580,
    profit: 1374,
    buyerName: '张先生',
    buyerAvatar: '/placeholder.svg?height=40&width=40',
    buyerPhone: '138****8888',
    orderTime: '2024-12-01 14:30',
    paymentTime: '2024-12-01 14:35',
    deliveryTime: '2024-12-02 10:15',
    completedTime: null, // Not completed yet
    deliveryAddress: '广东省深圳市南山区科技园南区',
    image: '/placeholder.svg?height=80&width=80',
    urgentLevel: 'urgent',
    courierCompany: '顺丰速运',
    trackingNumber: 'SF1234567890',
    remarks: '请尽快发货，客户急需使用。包装要求加固，避免运输损坏。',
    ratingComment: '产品质量很好，物流速度快，卖家服务热情周到，非常满意！',
    rating: 5,
  });

  // 获取订单状态在步骤中的索引
  const getOrderStatusIndex = (status) => {
    return orderSteps.findIndex(step => step.status === status);
  };

  const orderStatusIndex = computed(() => getOrderStatusIndex(order.value.status));

  // 检查步骤是否完成 (之前的步骤)
  const isStepCompleted = (index) => {
    return orderStatusIndex.value > index;
  };

  // 检查是否当前步骤
  const isCurrentStep = (index) => {
    return orderStatusIndex.value === index;
  };

  // 获取进度条宽度
  const getProgressWidth = () => {
    if (orderStatusIndex.value < 0) return '0%';
    const totalSteps = orderSteps.length - 1;
    if (totalSteps <= 0) return '0%';
    // The progress should be proportional to the segments between steps
    const percentage = (orderStatusIndex.value / totalSteps) * 100;
    return `${Math.max(0, Math.min(100, percentage))}%`;
  };


  // 初始化步骤时间
  const initStepTimes = () => {
    if (order.value.orderTime) {
      orderSteps[0].time = order.value.orderTime;
    }
    if (order.value.paymentTime) {
      orderSteps[1].time = order.value.paymentTime;
    }
    if (order.value.deliveryTime) {
      orderSteps[2].time = order.value.deliveryTime;
    }
    if (order.value.completedTime) {
      orderSteps[3].time = order.value.completedTime;
    }
  };

  // 页面加载时初始化步骤时间
  onMounted(() => {
    initStepTimes();
  });
</script>

<style scoped lang="scss">
@keyframes pulse-blue {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
}

.animate-pulse-blue {
  animation: pulse-blue 2s infinite;
}
</style>
