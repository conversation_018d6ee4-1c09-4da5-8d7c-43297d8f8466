<template>
  <ly-layout>
    <!-- 记录卡片列表 -->
    <div class="space-y-3 p-4">
      <div v-for="record in records" :key="record.id" class="bg-white rounded-lg shadow-sm border border-gray-200 p-4" @click="viewRecord(record)">
        <div class="flex items-start space-x-3">
          <!-- 商品图片 -->
          <div class="w-16 h-16 flex-shrink-0">
            <img class="w-16 h-16 rounded-lg object-cover" :src="record.productImage" :alt="record.productName" />
          </div>

          <!-- 商品信息 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h4 class="text-sm font-medium text-gray-900 truncate">{{ record.productName }}</h4>
                <div class="text-xs text-gray-500 mt-1">SKU: {{ record.sku }}</div>
                <div class="text-xs text-gray-500">{{ record.supplier }}</div>
              </div>
              <div :class="getStatusClass(record.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ml-2">
                {{ getStatusText(record.status) }}
              </div>
            </div>

            <!-- 数量和金额信息 -->
            <div class="mt-3 grid grid-cols-3 gap-4 text-sm">
              <div>
                <div class="text-gray-500 text-xs">数量</div>
                <div class="font-medium text-gray-900">{{ record.quantity }}</div>
              </div>
              <div>
                <div class="text-gray-500 text-xs">单价</div>
                <div class="font-medium text-gray-900">¥{{ record.unitPrice.toFixed(2) }}</div>
              </div>
              <div>
                <div class="text-gray-500 text-xs">总金额</div>
                <div class="font-medium text-blue-600">¥{{ record.totalAmount.toFixed(2) }}</div>
              </div>
            </div>

            <!-- 底部信息 -->
            <div class="mt-3 flex items-center justify-between text-xs text-gray-500">
              <div>{{ record.orderNo }}</div>
              <div>{{ formatDate(record.createdAt) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  // 统计数据
  const stats = ref({
    todayCount: 12,
    monthCount: 68,
    totalAmount: 24500,
    pendingCount: 3,
  });

  // 分页数据
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(68);
  const hasMore = computed(() => records.value.length < total.value);

  // 入库记录数据
  const records = ref([
    {
      id: 1,
      orderNo: 'IN202412140001',
      productName: '人参',
      sku: 'RS-001-AAA',
      productImage: 'https://picsum.photos/200/300?random=1',
      supplier: '长白山药材供应商',
      quantity: 50,
      unitPrice: 120.0,
      totalAmount: 6000.0,
      status: 'completed',
      createdAt: '2024-12-14 10:30:00',
    },
    {
      id: 2,
      orderNo: 'IN202412140002',
      productName: '当归',
      sku: 'DG-002-A',
      productImage: 'https://picsum.photos/200/300?random=2',
      supplier: '甘肃岷县药材基地',
      quantity: 100,
      unitPrice: 35.0,
      totalAmount: 3500.0,
      status: 'pending',
      createdAt: '2024-12-14 09:15:00',
    },
    {
      id: 3,
      orderNo: 'IN202412140003',
      productName: '枸杞子',
      sku: 'GQZ-003-AA',
      productImage: 'https://picsum.photos/200/300?random=3',
      supplier: '宁夏中宁枸杞合作社',
      quantity: 200,
      unitPrice: 28.0,
      totalAmount: 5600.0,
      status: 'completed',
      createdAt: '2024-12-14 08:45:00',
    },
    {
      id: 4,
      orderNo: 'IN202412140004',
      productName: '黄芪',
      sku: 'HQ-004-A',
      productImage: 'https://picsum.photos/200/300?random=4',
      supplier: '内蒙古黄芪种植基地',
      quantity: 80,
      unitPrice: 45.0,
      totalAmount: 3600.0,
      status: 'processing',
      createdAt: '2024-12-13 16:20:00',
    },
    {
      id: 5,
      orderNo: 'IN202412140005',
      productName: '三七',
      sku: 'SQ-005-AAA',
      productImage: 'https://picsum.photos/200/300?random=5',
      supplier: '云南文山三七种植园',
      quantity: 30,
      unitPrice: 180.0,
      totalAmount: 5400.0,
      status: 'completed',
      createdAt: '2024-12-13 14:10:00',
    },
  ]);

  // 状态相关方法
  const getStatusClass = (status: string) => {
    const statusClasses = {
      completed: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800',
      rejected: 'bg-red-100 text-red-800',
    };
    return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
  };

  const getStatusText = (status: string) => {
    const statusTexts = {
      completed: '已完成',
      pending: '待审核',
      processing: '处理中',
      rejected: '已拒绝',
    };
    return statusTexts[status as keyof typeof statusTexts] || '未知';
  };

  // 日期格式化
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 查看记录详情
  const viewRecord = (record: any) => {
    console.log('查看记录详情:', record);
    monkey.$router.navigateTo('/modules/user/inbound/detail?id=' + record.id);
  };

  // 加载更多数据
  const loadMore = () => {
    console.log('加载更多数据');
    // 这里可以调用 API 加载更多数据
    // 模拟加载更多数据
    currentPage.value++;
  };

  onMounted(() => {
    // 初始化数据加载
    console.log('入库记录页面已加载');
  });
</script>

<style scoped>
  /* 自定义样式可以在这里添加 */
</style>
