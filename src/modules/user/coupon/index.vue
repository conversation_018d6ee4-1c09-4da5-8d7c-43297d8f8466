<template>
  <ly-layout>
    <div class="px-4 py-4">
      <!-- 标签切换 -->
      <div class="flex bg-gray-100 rounded-lg p-1 mb-4">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          @click="activeTab = index"
          class="flex-1 text-center py-2 rounded-md transition-all duration-200"
          :class="{
            'bg-white text-theme-blue font-semibold shadow-xs': activeTab === index,
            'text-gray-600 text-xs': activeTab !== index,
          }"
        >
          {{ tab }}
        </div>
      </div>

      <!-- 优惠券列表 -->
      <div class="space-y-3">
        <div v-for="coupon in filteredCoupons" :key="coupon.id" class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden" @click="monkey.$router.navigateTo(`/modules/user/coupon/detail?id=${coupon.id}`)">
          <!-- 优惠券卡片 -->
          <div class="flex">
            <!-- 左侧金额区域 -->
            <div
              class="w-24 flex flex-col items-center justify-center text-white relative"
              :class="{
                'bg-theme-blue': coupon.status === 'available',
                'bg-gray-400': coupon.status === 'expired',
                'bg-orange-400': coupon.status === 'used',
              }"
            >
              <div class="text-xs opacity-90">￥</div>
              <div class="text-xl font-bold">{{ coupon.amount }}</div>
              <div class="text-xs opacity-90">满{{ coupon.minAmount }}元可用</div>

              <!-- 圆形缺口效果 -->
              <div class="absolute -right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-gray-50 rounded-full"></div>
            </div>

            <!-- 右侧信息区域 -->
            <div class="flex-1 p-4">
              <div class="flex justify-between items-start mb-2">
                <div>
                  <div class="font-semibold text-gray-800 text-base">{{ coupon.title }}</div>
                  <!-- <div class="text-sm text-gray-500 mt-1">{{ coupon.description }}</div> -->
                </div>
                <div
                  class="px-2 py-1 rounded-full text-xs font-medium"
                  :class="{
                    'bg-theme-blue/10 text-theme-blue': coupon.status === 'available',
                    'bg-gray-100 text-gray-500': coupon.status === 'expired',
                    'bg-orange-100 text-orange-600': coupon.status === 'used',
                  }"
                >
                  {{ getStatusText(coupon.status) }}
                </div>
              </div>

              <div class="flex justify-between items-center text-sm">
                <div class="text-gray-400">
                  有效期至：{{ formatDate(coupon.expireDate) }}
                </div>
              </div>

              <div v-if="coupon.status === 'available'" class="flex justify-end mt-3">
                <div @click.stop="useCoupon(coupon)" class="px-4 py-1.5 bg-theme-blue text-white text-sm rounded-full font-medium transition-all duration-200 hover:bg-theme-blue-600 active:scale-95">
                  立即使用
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredCoupons.length === 0" class="text-center py-16">
        <div class="i-mdi-ticket-outline text-6xl text-gray-300 mb-4"></div>
        <div class="text-gray-500 text-lg">暂无优惠券</div>
        <div class="text-gray-400 text-sm mt-2">快去领取优惠券吧～</div>
        <div @click="goToCouponCenter" class="mt-4 px-6 py-2 bg-theme-blue text-white rounded-lg font-medium transition-all duration-200 hover:bg-theme-blue-600 active:scale-95">去领取</div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  interface Coupon {
    id: string;
    title: string;
    description: string;
    amount: number;
    minAmount: number;
    expireDate: string;
    status: 'available' | 'used' | 'expired';
  }

  const activeTab = ref(0);
  const tabs = ['可用', '已使用', '已过期'];

  const couponStats = ref({
    available: 3,
    used: 5,
    expired: 2,
  });

  const coupons = ref<Coupon[]>([
    {
      id: '1',
      title: '新用户专享',
      description: '首次下单立减',
      amount: 50,
      minAmount: 200,
      expireDate: '2024-12-31',
      status: 'available',
    },
    {
      id: '2',
      title: '满减优惠',
      description: '购买药材专用',
      amount: 100,
      minAmount: 500,
      expireDate: '2024-11-30',
      status: 'available',
    },
    {
      id: '3',
      title: '仓储服务券',
      description: '仓储费用减免',
      amount: 30,
      minAmount: 100,
      expireDate: '2024-10-31',
      status: 'used',
    },
    {
      id: '4',
      title: '过期优惠券',
      description: '已过期无法使用',
      amount: 20,
      minAmount: 150,
      expireDate: '2024-08-31',
      status: 'expired',
    },
  ]);

  const filteredCoupons = computed(() => {
    const statusMap = ['available', 'used', 'expired'];
    return coupons.value.filter((coupon) => coupon.status === statusMap[activeTab.value]);
  });

  const handleBack = () => {
    uni.navigateBack();
  };

  const getStatusText = (status: string) => {
    const statusTexts = {
      available: '可用',
      used: '已使用',
      expired: '已过期',
    };
    return statusTexts[status] || '';
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`;
  };

  const useCoupon = (coupon: Coupon) => {
    uni.showModal({
      title: '使用优惠券',
      content: `确定要使用${coupon.amount}元优惠券吗？`,
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到订单页面或产品页面
          monkey.$router.navigateTo('/modules/goods/list/index');
        }
      },
    });
  };

  const goToCouponCenter = () => {
    // 跳转到优惠券领取中心
    uni.showToast({
      title: '功能开发中',
      icon: 'none',
    });
  };
</script>

<style scoped lang="scss">
  // 优惠券圆形缺口样式
  .coupon-notch {
    position: relative;

    &::before,
    &::after {
      content: '';
      position: absolute;
      right: -8px;
      width: 16px;
      height: 16px;
      background: #f9fafb;
      border-radius: 50%;
    }

    &::before {
      top: -8px;
    }

    &::after {
      bottom: -8px;
    }
  }
</style>
