<template>
  <ly-layout>
    <div class="bg-gray-50 min-h-screen">
      <!-- 优惠券卡片 -->
      <div class="p-4">
        <div class="bg-gradient-to-r from-theme-blue to-theme-blue-600 rounded-xl shadow-lg overflow-hidden">
          <!-- 优惠券主体 -->
          <div class="relative">
            <!-- 背景装饰 -->
            <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
            <div class="absolute top-2 right-2 w-20 h-20 bg-white/5 rounded-full blur-xl"></div>
            <div class="absolute bottom-2 left-2 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>

            <!-- 优惠券内容 -->
            <div class="relative z-10 p-5">
              <!-- 顶部状态 -->
              <div class="flex justify-between items-center mb-4">
                <div
                  class="px-3 py-1 rounded-full text-xs font-medium"
                  :class="{
                    'bg-white/20 text-white': coupon.status === 'available',
                    'bg-gray-800/20 text-gray-300': coupon.status === 'expired',
                    'bg-orange-200/20 text-orange-100': coupon.status === 'used',
                  }"
                >
                  {{ getStatusText(coupon.status) }}
                </div>
                <div class="text-right">
                  <div class="text-xs text-white/80">券码</div>
                  <div class="text-sm font-mono font-bold text-white">{{ coupon.code }}</div>
                </div>
              </div>

              <!-- 金额区域 -->
              <div class="text-center mb-4">
                <div class="inline-flex items-baseline gap-1">
                  <span class="text-xl font-bold text-white">￥</span>
                  <span class="text-5xl font-black text-white drop-shadow-sm">{{ coupon.amount }}</span>
                </div>
                <div class="text-white text-lg font-bold mt-2 drop-shadow-sm">{{ coupon.title }}</div>
                <div class="text-white/90 text-sm mt-1">{{ coupon.description }}</div>
              </div>

              <!-- 装饰性分割线 -->
              <div class="relative my-4">
                <div class="h-px bg-white/20 w-full"></div>
                <div class="absolute left-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white/30 rounded-full -ml-1.5 border border-white/40"></div>
                <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white/30 rounded-full -mr-1.5 border border-white/40"></div>
              </div>

              <!-- 使用条件 -->
              <div class="flex justify-between items-center text-sm">
                <div class="flex items-center gap-1">
                  <div class="w-1.5 h-1.5 bg-white rounded-full"></div>
                  <span class="text-white/90"
                    >满<span class="font-bold text-white">{{ coupon.minAmount }}</span
                    >元可用</span
                  >
                </div>
                <div class="text-white/80">
                  {{ formatDate(coupon.expireDate) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用规则 -->
      <div class="px-4 mb-4">
        <div class="bg-white rounded-xl p-4">
          <div class="flex items-center gap-2 mb-3">
            <div class="w-6 h-6 bg-theme-blue/10 rounded-full flex items-center justify-center">
              <div class="i-mdi-information-outline text-theme-blue text-sm"></div>
            </div>
            <div class="text-base font-semibold text-gray-800">使用规则</div>
          </div>

          <div class="space-y-2">
            <div v-for="(rule, index) in coupon.rules" :key="index" class="flex items-start gap-2">
              <div class="w-4 h-4 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <div class="text-xs font-medium text-gray-500">{{ index + 1 }}</div>
              </div>
              <div class="text-gray-600 text-sm leading-relaxed">{{ rule }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 适用产品 -->
      <div class="px-4 mb-20">
        <div class="bg-white rounded-xl p-4">
          <div class="flex items-center gap-2 mb-3">
            <div class="w-6 h-6 bg-blue-50 rounded-full flex items-center justify-center">
              <div class="i-mdi-package-variant text-blue-600 text-sm"></div>
            </div>
            <div class="text-base font-semibold text-gray-800">适用产品</div>
          </div>

          <div class="flex flex-wrap gap-2">
            <div v-for="category in coupon.categories" :key="category" class="px-3 py-1 bg-blue-50 text-blue-600 rounded-lg text-sm font-medium">
              {{ category }}
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区 -->
      <ly-fixed-btns type="primary" text="立即使用" v-if="coupon.status === 'available'" @click="useCoupon"> </ly-fixed-btns>
      <ly-fixed-btns v-else>
        <div class="w-full h-12 flex items-center justify-center bg-gray-200 text-gray-500 rounded-lg font-semibold">
          {{ coupon.status === 'used' ? '已使用' : '已过期' }}
        </div>
      </ly-fixed-btns>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  interface Coupon {
    id: string;
    code: string;
    title: string;
    description: string;
    amount: number;
    minAmount: number;
    expireDate: string;
    status: 'available' | 'used' | 'expired';
    rules: string[];
    categories: string[];
  }

  const coupon = ref<Coupon>({
    id: '1',
    code: 'NEWUSER50',
    title: '新用户专享',
    description: '首次下单立减优惠券',
    amount: 50,
    minAmount: 200,
    expireDate: '2024-12-31',
    status: 'available',
    rules: ['本优惠券仅限新用户首次下单使用', '订单金额满200元方可使用', '不可与其他优惠券同时使用', '优惠券使用后不可退换', '如有疑问请联系客服'],
    categories: ['中药材', '西药', '保健品', '医疗器械'],
  });

  const getStatusText = (status: string) => {
    const statusTexts = {
      available: '可使用',
      used: '已使用',
      expired: '已过期',
    };
    return statusTexts[status] || '';
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return '已过期';
    } else if (diffDays === 0) {
      return '今天到期';
    } else if (diffDays === 1) {
      return '明天到期';
    } else if (diffDays <= 7) {
      return `${diffDays}天后到期`;
    } else {
      return `有效期至 ${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`;
    }
  };

  const useCoupon = () => {
    uni.showModal({
      title: '使用优惠券',
      content: `确定要使用${coupon.value.amount}元优惠券吗？`,
      success: (res) => {
        if (res.confirm) {
          monkey.$router.navigateTo('/modules/goods/list/index');
        }
      },
    });
  };

  onMounted(() => {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options;

    if (options.id) {
      console.log('优惠券ID:', options.id);
    }
  });
</script>

<style scoped lang="scss">
  .coupon-bg {
    background-image: radial-gradient(circle at 20% 50%, rgba(34, 197, 94, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.05) 0%, transparent 50%);
  }
</style>
