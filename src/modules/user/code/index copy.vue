<template>
  <ly-layout>
    <view class="px-4 py-8">
      <!-- 顶部图标和标题区域 -->
      <view class="flex flex-col items-center mb-12">
        <!-- 验证码图标 -->
        <view class="w-20 h-20 bg-theme-blue/10 rounded-full flex items-center justify-center mb-6">
          <text class="i-mdi-shield-check text-theme-blue text-4xl"></text>
        </view>
        
        <!-- 标题和描述 -->
        <view class="text-center mb-2">
          <view class="text-2xl font-bold text-gray-800 mb-2">验证码验证</view>
          <view class="text-gray-600 text-sm leading-relaxed">
            <text>验证码已发送至：</text>
            <text class="font-medium text-theme-blue">{{ maskPhone(phoneNumber) }}</text>
          </view>
        </view>
      </view>

      <!-- 验证码输入区域 -->
      <view class="mb-8">
        <view class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
          <!-- 装饰性背景 -->
          <view class="absolute inset-0 bg-gradient-to-br from-theme-blue/5 via-transparent to-theme-blue/3 rounded-2xl"></view>
          
          <view class="relative z-10">
            <!-- 验证码输入框标题 -->
            <view class="text-sm font-medium text-gray-700 mb-4 text-center">请输入6位验证码</view>
            
            <!-- 6位验证码输入框 -->
            <view class="flex justify-center gap-3 mb-6">
              <view 
                v-for="(digit, index) in codeDigits" 
                :key="index"
                class="w-12 h-12 border-2 rounded-xl flex items-center justify-center text-lg font-bold transition-all duration-200"
                :class="[
                  digit ? 'border-theme-blue bg-theme-blue/5 text-theme-blue' : 'border-gray-200 bg-gray-50',
                  focusIndex === index ? 'border-theme-blue ring-2 ring-theme-blue/20' : '',
                  isError ? 'border-red-500 bg-red-50 text-red-500' : ''
                ]"
              >
                <text v-if="digit">{{ digit }}</text>
                <view v-else-if="focusIndex === index" class="w-0.5 h-6 bg-theme-blue animate-pulse"></view>
              </view>
            </view>
            
            <!-- 隐藏的实际输入框 -->
            <input 
              v-model="verificationCode"
              type="number"
              maxlength="6"
              class="opacity-0 absolute -left-full"
              :focus="isFocused"
              @input="handleInput"
              @focus="handleFocus"
              @blur="handleBlur"
            />
            
            <!-- 点击区域 -->
            <view 
              class="absolute inset-0 z-20" 
              @click="handleClickInput"
            ></view>
          </view>
        </view>
      </view>

      <!-- 功能信息和状态区域 -->
      <view class="mb-8">
        <!-- 错误提示 -->
        <view v-if="errorMessage" class="mb-4">
          <view class="bg-red-50 border border-red-200 rounded-xl p-4 flex items-center">
            <text class="i-mdi-alert-circle text-red-500 text-lg mr-3"></text>
            <text class="text-red-600 text-sm">{{ errorMessage }}</text>
          </view>
        </view>

        <!-- 验证成功提示 -->
        <view v-if="isSuccess" class="mb-4">
          <view class="bg-green-50 border border-green-200 rounded-xl p-4 flex items-center">
            <text class="i-mdi-check-circle text-green-500 text-lg mr-3"></text>
            <text class="text-green-600 text-sm">验证成功，正在跳转...</text>
          </view>
        </view>

        <!-- 倒计时和重新发送 -->
        <view class="flex items-center justify-center">
          <view v-if="countdown > 0" class="text-gray-500 text-sm">
            <text>{{ countdown }}s后可重新发送</text>
          </view>
          <view 
            v-else
            @click="handleResendCode"
            class="text-theme-blue text-sm font-medium active:text-theme-blue-600 transition-colors cursor-pointer"
          >
            重新发送验证码
          </view>
        </view>
      </view>

      <!-- 验证按钮 -->
      <view class="mb-6">
        <view 
          @click="handleVerify"
          class="cu-btn-style"
          :class="[
            !canVerify ? 'opacity-50' : 'active:scale-95'
          ]"
        >
          <text v-if="isVerifying" class="i-mdi-loading animate-spin mr-2"></text>
          {{ isVerifying ? '验证中...' : '确认验证' }}
        </view>
      </view>

      <!-- 底部提示信息 -->
      <view class="text-center">
        <view class="text-xs text-gray-400 leading-relaxed">
          <text>验证码5分钟内有效</text>
          <text class="block mt-1">如果您没有收到验证码，请检查短信或重新发送</text>
        </view>
      </view>
    </view>
  </ly-layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 响应式数据
const verificationCode = ref('')
const phoneNumber = ref('13800138000') // 示例手机号
const focusIndex = ref(0)
const isFocused = ref(false)
const countdown = ref(60)
const isVerifying = ref(false)
const isError = ref(false)
const isSuccess = ref(false)
const errorMessage = ref('')

// 计时器
let countdownTimer: NodeJS.Timeout | null = null

// 计算属性
const codeDigits = computed(() => {
  const digits = verificationCode.value.split('')
  return Array.from({ length: 6 }, (_, index) => digits[index] || '')
})

const canVerify = computed(() => {
  return verificationCode.value.length === 6 && !isVerifying.value
})

// 方法
const maskPhone = (phone: string) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

const handleInput = (event: any) => {
  const value = event.detail.value.replace(/[^\d]/g, '').slice(0, 6)
  verificationCode.value = value
  focusIndex.value = value.length
  
  // 清除错误状态
  if (isError.value) {
    isError.value = false
    errorMessage.value = ''
  }
  
  // 自动验证
  if (value.length === 6) {
    setTimeout(() => {
      handleVerify()
    }, 300)
  }
}

const handleFocus = () => {
  isFocused.value = true
}

const handleBlur = () => {
  isFocused.value = false
}

const handleClickInput = () => {
  // 聚焦到隐藏的输入框
  const input = document.querySelector('input[type="number"]') as HTMLInputElement
  if (input) {
    input.focus()
  }
}

const handleVerify = async () => {
  if (!canVerify.value) return
  
  isVerifying.value = true
  isError.value = false
  errorMessage.value = ''
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 模拟验证结果
    const isValidCode = verificationCode.value === '123456' // 示例验证逻辑
    
    if (isValidCode) {
      isSuccess.value = true
      uni.showToast({
        title: '验证成功',
        icon: 'success'
      })
      
      // 验证成功后的跳转逻辑
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    } else {
      isError.value = true
      errorMessage.value = '验证码错误，请重新输入'
      // 清空验证码
      verificationCode.value = ''
      focusIndex.value = 0
    }
  } catch (error) {
    isError.value = true
    errorMessage.value = '验证失败，请重试'
  } finally {
    isVerifying.value = false
  }
}

const handleResendCode = async () => {
  if (countdown.value > 0) return
  
  try {
    // 模拟发送验证码API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    uni.showToast({
      title: '验证码已发送',
      icon: 'success'
    })
    
    // 开始倒计时
    startCountdown()
  } catch (error) {
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'error'
    })
  }
}

const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer!)
      countdownTimer = null
    }
  }, 1000)
}

// 生命周期
onMounted(() => {
  // 页面加载时开始倒计时
  startCountdown()
  
  // 自动聚焦
  setTimeout(() => {
    handleClickInput()
  }, 500)
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped lang="scss">
// 自定义动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

// 确保输入框完全隐藏但仍可交互
input[type="number"] {
  position: absolute;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}
</style>