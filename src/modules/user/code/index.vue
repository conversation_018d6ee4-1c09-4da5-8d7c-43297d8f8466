<template>
  <ly-layout>
    <view class="px-4 py-6">
      <!-- 顶部图标和标题区域 -->
      <view class="flex flex-col items-center mb-8">
        <!-- 验证码图标 -->
        <view class="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-4">
          <text class="i-mdi-shield-check text-blue-600 text-2xl"></text>
        </view>

        <!-- 标题和描述 -->
        <view class="text-center">
          <view class="text-lg font-medium text-gray-800 mb-1">验证码信息</view>
          <view class="text-gray-500 text-sm"> 请使用以下验证码完成验证 </view>
        </view>
      </view>

      <!-- 验证码展示区域 -->
      <view class="mb-8">
        <view class="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
          <!-- 验证码标题 -->
          <view class="text-sm text-gray-600 mb-4 text-center">您的验证码</view>

          <!-- 6位验证码展示 -->
          <view v-if="!loading" class="flex justify-center gap-3 mb-6">
            <view v-for="(digit, index) in displayCode" :key="index" class="w-10 h-10 border border-blue-200 bg-blue-50 rounded-md flex items-center justify-center text-lg font-mono font-medium text-blue-700">
              {{ digit }}
            </view>
          </view>

          <!-- 加载状态 -->
          <view v-else class="flex justify-center gap-3 mb-6">
            <view v-for="index in 6" :key="index" class="w-10 h-10 border border-gray-200 bg-gray-100 rounded-md flex items-center justify-center">
              <view class="w-3 h-3 bg-gray-300 rounded animate-pulse"></view>
            </view>
          </view>

          <!-- 复制按钮 -->
          <view class="flex items-center justify-center">
            <view
              @click="handleCopyCode"
              :class="['bg-blue-50 active:bg-blue-100 text-blue-700 font-medium py-2 px-4 rounded-md border border-blue-200 transition-all duration-200 cursor-pointer text-sm', loading ? 'opacity-50 cursor-not-allowed' : 'active:scale-95']"
            >
              <text class="i-mdi-content-copy mr-2"></text>
              复制验证码
            </view>
          </view>
        </view>
      </view>

      <!-- 过期时间显示 -->
      <view class="mb-6">
        <view class="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <view class="flex items-center justify-between">
            <view class="flex items-center">
              <text class="i-mdi-timer-outline text-blue-500 text-base mr-2"></text>
              <text class="text-sm text-gray-600">过期时间</text>
            </view>
            <text class="text-sm text-gray-700">{{ expireTimeSeconds }}分钟</text>
          </view>
        </view>
      </view>

      <!-- 成功提示 -->
      <view v-if="copySuccess" class="mb-4">
        <view class="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center">
          <text class="i-mdi-check-circle text-green-600 text-base mr-2"></text>
          <text class="text-green-700 text-sm">验证码已复制到剪贴板</text>
        </view>
      </view>

      <!-- 底部提示信息 -->
      <view class="text-center">
        <view class="text-xs text-gray-400 leading-relaxed">
          <text>验证码仅用于当前操作，请妥善保管</text>
          <text class="block mt-1">如需重新获取，请重新进入页面</text>
        </view>
      </view>
    </view>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  // 响应式数据
  const verificationCode = ref('');
  const copySuccess = ref(false);
  const loading = ref(false);
  const expireTimeSeconds = ref(0);

  // 计算属性
  const displayCode = computed(() => {
    return verificationCode.value.split('');
  });

  // 方法
  const formatTime = (time: Date) => {
    return monkey.$dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  };

  const handleCopyCode = async () => {
    if (loading.value || !verificationCode.value) return;

    try {
      // 复制到剪贴板
      await uni.setClipboardData({
        data: verificationCode.value,
      });

      copySuccess.value = true;
      uni.showToast({
        title: '复制成功',
        icon: 'success',
      });

      // 3秒后隐藏成功提示
      setTimeout(() => {
        copySuccess.value = false;
      }, 3000);
    } catch (error) {
      uni.showToast({
        title: '复制失败',
        icon: 'error',
      });
    }
  };

  // 获取验证码接口
  const getCode = async () => {
    try {
      loading.value = true;
      monkey.$helper.toast.loading('获取验证码中...');

      const response = await monkey.$api.user.getCode();
      if (response.errcode === 200 && response.data) {
        // 如果返回的是字符串验证码
        verificationCode.value = response.data;
        // 设置获取时间 计算分钟数
        expireTimeSeconds.value = response.msg / 60;

        monkey.$helper.toast.success('验证码获取成功');
      } else {
        monkey.$helper.toast.error(response.errmsg || '获取验证码失败');
      }
    } catch (error) {
      console.error('获取验证码失败:', error);
      monkey.$helper.toast.error('获取验证码失败');
    } finally {
      loading.value = false;
      monkey.$helper.toast.hideLoading();
    }
  };

  // 生命周期
  onLoad(() => {
    // 调用接口获取验证码
    getCode();
  });
</script>

<style scoped lang="scss"></style>
