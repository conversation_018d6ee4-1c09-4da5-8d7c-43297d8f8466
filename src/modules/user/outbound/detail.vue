<template>
  <div class="min-h-screen bg-gray-50">
    <div class="p-4 space-y-4">
      <!-- 药材主卡片 -->
      <div class="bg-white rounded-2xl p-6 shadow-lg border border-white/20">
        <div class="flex items-start space-x-4">
          <div class="relative">
            <div class="w-20 h-20 bg-green-500 rounded-2xl flex items-center justify-center shadow-lg">
              <div class="text-3xl">🌿</div>
            </div>
            <div :class="getStatusClass(record.status)" class="absolute -top-2 -right-2 px-2 py-1 text-xs font-bold rounded-full shadow-sm">
              {{ getStatusText(record.status) }}
            </div>
          </div>
          <div class="flex-1">
            <h1 class="text-xl font-bold text-gray-900 mb-1">{{ record.productName }}</h1>
            <div class="text-sm text-green-600 font-medium mb-1">{{ getOrigin(record.productName) }}</div>
            <div class="text-sm text-gray-600">{{ record.sku }}</div>
            <div class="text-xs text-gray-500 mt-2">{{ record.supplier }}</div>
          </div>
        </div>
        
        <!-- 核心数据 -->
        <div class="mt-6 grid grid-cols-3 gap-4">
          <div class="text-center p-3 bg-gray-50 rounded-xl">
            <div class="text-2xl font-bold text-gray-900">{{ record.quantity }}</div>
            <div class="text-xs text-gray-600 font-medium">KG</div>
          </div>
          <div class="text-center p-3 bg-gray-50 rounded-xl">
            <div class="text-lg font-bold text-gray-900">¥{{ record.unitPrice.toFixed(0) }}</div>
            <div class="text-xs text-gray-600 font-medium">单价/kg</div>
          </div>
          <div class="text-center p-3 bg-gray-50 rounded-xl">
            <div class="text-lg font-bold text-green-600">¥{{ (record.totalAmount/1000).toFixed(1) }}k</div>
            <div class="text-xs text-gray-600 font-medium">总金额</div>
          </div>
        </div>
      </div>

      <!-- 详细信息卡片 -->
      <div class="grid grid-cols-2 gap-3">
        <div class="bg-white rounded-xl p-4 border border-gray-200">
          <div class="flex items-center space-x-2 mb-2">
            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
            <div class="text-xs font-medium text-gray-600">入库时间</div>
          </div>
          <div class="text-sm font-semibold text-gray-900">{{ formatDateShort(record.createdAt) }}</div>
        </div>
        
        <div class="bg-white rounded-xl p-4 border border-gray-200">
          <div class="flex items-center space-x-2 mb-2">
            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
            <div class="text-xs font-medium text-gray-600">仓库位置</div>
          </div>
          <div class="text-sm font-semibold text-gray-900">{{ record.warehouse || 'A区-01' }}</div>
        </div>
        
        <div class="bg-white rounded-xl p-4 border border-gray-200">
          <div class="flex items-center space-x-2 mb-2">
            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
            <div class="text-xs font-medium text-gray-600">批次号</div>
          </div>
          <div class="text-sm font-semibold text-gray-900">{{ (record.batchNo || 'B20241214001').slice(-6) }}</div>
        </div>
        
        <div class="bg-white rounded-xl p-4 border border-gray-200">
          <div class="flex items-center space-x-2 mb-2">
            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
            <div class="text-xs font-medium text-gray-600">有效期</div>
          </div>
          <div class="text-sm font-semibold text-gray-900">{{ formatDateShort(record.expiryDate || '2026-12-14') }}</div>
        </div>
      </div>

      <!-- 质检与供应商 -->
      <div class="space-y-3">
        <div class="bg-white rounded-xl p-4 border border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                <div class="text-white text-sm font-bold">✓</div>
              </div>
              <div>
                <div class="text-sm font-semibold text-gray-900">质检合格</div>
                <div class="text-xs text-gray-500">李质检 · {{ formatTimeShort(record.createdAt) }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl p-4 border border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gray-500 rounded-full flex items-center justify-center">
                <div class="text-white text-sm">👤</div>
              </div>
              <div>
                <div class="text-sm font-semibold text-gray-900">{{ getSupplierContact(record.supplier) }}</div>
                <div class="text-xs text-gray-500">{{ record.supplier }}</div>
              </div>
            </div>
            <div class="bg-green-500 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-sm">
              {{ getSupplierPhone(record.supplier) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 操作记录 -->
      <div class="bg-white rounded-xl border border-gray-200 overflow-hidden">
        <div class="p-4 bg-gray-50 border-b border-gray-200">
          <div class="text-sm font-semibold text-gray-900">操作记录</div>
        </div>
        <div class="p-4">
          <div class="space-y-4">
            <div v-for="(log, index) in operationLogs.slice(0, 3)" :key="log.id" class="flex items-start space-x-3">
              <div class="flex flex-col items-center">
                <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                <div v-if="index < 2" class="w-0.5 h-8 bg-gray-200 mt-1"></div>
              </div>
              <div class="flex-1">
                <div class="text-sm font-medium text-gray-900">{{ log.action }}</div>
                <div class="text-xs text-gray-500 mt-1 flex items-center space-x-1">
                  <div>{{ log.operator }}</div>
                  <div class="w-1 h-1 bg-gray-300 rounded-full"></div>
                  <div>{{ formatTimeShort(log.time) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作 -->
    <div v-if="record.status === 'pending'" class="fixed bottom-0 left-0 right-0 p-4">
      <div class="bg-white rounded-2xl p-4 shadow-xl border border-gray-200">
        <div class="flex space-x-3">
          <button 
            @click="approveRecord"
            class="flex-1 bg-green-500 text-white py-3 rounded-xl text-sm font-semibold shadow-lg hover:bg-green-600 transition-all"
          >
            审核通过
          </button>
          <button 
            @click="rejectRecord"
            class="flex-1 bg-red-500 text-white py-3 rounded-xl text-sm font-semibold shadow-lg hover:bg-red-600 transition-all"
          >
            审核拒绝
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 入库记录详情数据
const record = ref({
  id: 1,
  orderNo: 'IN202412140001',
  productName: '人参',
  sku: 'RS-001-AAA',
  productImage: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=200&h=200&fit=crop&crop=center',
  supplier: '长白山药材供应商',
  quantity: 50,
  unitPrice: 120.0,
  totalAmount: 6000.0,
  status: 'completed',
  createdAt: '2024-12-14 10:30:00',
  operator: '张三',
  warehouse: 'A区-01货架',
  batchNo: 'B20241214001',
  expiryDate: '2026-12-14',
  remark: '该批次人参质量优良，参体完整，无虫蛀霉变，符合入库标准。存储时请注意防潮防虫，定期检查质量状况。'
})

// 操作记录
const operationLogs = ref([
  {
    id: 1,
    action: '创建入库单',
    operator: '张三',
    time: '2024-12-14 10:30:00'
  },
  {
    id: 2,
    action: '质检完成',
    operator: '李质检',
    time: '2024-12-14 11:15:00'
  },
  {
    id: 3,
    action: '审核通过',
    operator: '王主管',
    time: '2024-12-14 11:45:00'
  },
  {
    id: 4,
    action: '入库完成',
    operator: '张三',
    time: '2024-12-14 12:00:00'
  }
])

// 状态相关方法
const getStatusClass = (status: string) => {
  const statusClasses = {
    completed: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const statusTexts = {
    completed: '已完成',
    pending: '待审核',
    processing: '处理中',
    rejected: '已拒绝'
  }
  return statusTexts[status as keyof typeof statusTexts] || '未知'
}

// 获取供应商联系人
const getSupplierContact = (supplier: string) => {
  const contacts = {
    '长白山药材供应商': '王经理',
    '甘肃岷县药材基地': '李经理',
    '宁夏中宁枸杞合作社': '张经理',
    '内蒙古黄芪种植基地': '赵经理',
    '云南文山三七种植园': '陈经理'
  }
  return contacts[supplier as keyof typeof contacts] || '联系人'
}

// 获取供应商电话
const getSupplierPhone = (supplier: string) => {
  const phones = {
    '长白山药材供应商': '138-0431-1234',
    '甘肃岷县药材基地': '139-0931-5678',
    '宁夏中宁枸杞合作社': '137-0951-9012',
    '内蒙古黄芪种植基地': '136-0471-3456',
    '云南文山三七种植园': '135-0871-7890'
  }
  return phones[supplier as keyof typeof phones] || '联系电话'
}

// 获取产地信息
const getOrigin = (productName: string) => {
  const origins = {
    '人参': '吉林长白山',
    '当归': '甘肃岷县',
    '枸杞子': '宁夏中宁',
    '黄芪': '内蒙古',
    '三七': '云南文山'
  }
  return origins[productName as keyof typeof origins] || '产地'
}

// 日期格式化
const formatDate = (dateString: string, addHours = false) => {
  const date = new Date(dateString)
  if (addHours) {
    date.setHours(date.getHours() + 1)
  }
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 简短日期格式化
const formatDateShort = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '昨天'
  if (diffDays < 7) return `${diffDays}天前`
  
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

// 简短时间格式化
const formatTimeShort = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 审核通过
const approveRecord = () => {
  console.log('审核通过')
  record.value.status = 'completed'
  // 这里可以调用API
}

// 审核拒绝
const rejectRecord = () => {
  console.log('审核拒绝')
  record.value.status = 'rejected'
  // 这里可以调用API
}

onMounted(() => {
  // 根据路由参数获取记录详情
  const recordId = route.params.id
  console.log('加载记录详情:', recordId)
  // 这里可以调用API获取详情数据
})
</script>

<style scoped>
/* 自定义样式 */
</style>