<template>
  <view class="bg-white rounded-xl p-6 shadow-md flex flex-col items-center">
    <view v-if="authResult.status === 'success'" class="text-center">
      <text class="iconfont icon-shenhetongguo_huaban1 text-8xl text-theme-blue"></text>
      <view class="text-xl font-bold mt-4 text-theme-blue">企业认证审核通过</view>
      <view class="text-gray-500 mt-3 text-center max-w-xs"> 
        恭喜您！企业认证已通过，您现在可以使用平台的所有功能 
      </view>
    </view>
    <view v-else class="text-center">
        <text class="iconfont icon-shenheshibai text-8xl text-red-500"></text>
      <view class="text-xl font-bold mt-4 text-theme-red">企业认证未通过</view>
      <view class="text-gray-500 mt-3 text-center max-w-xs"> 
        很遗憾，您的企业认证未通过，原因：{{ authResult.reason }} 
      </view>
      <div class="w-full bg-theme-blue active:bg-theme-blue-600 text-white py-3 rounded-lg mt-8 font-medium transition-colors duration-200 shadow-sm">
        重新提交认证
      </div>
    </view>
  </view>
</template>

<script setup lang="ts">
  const props = defineProps<{
    authResult: any;
  }>();
</script>

<style lang="scss" scoped></style>
