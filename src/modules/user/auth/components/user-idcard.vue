<template>
  <view class="flex gap-4">
    <!-- 身份证正面上传 -->
    <view class="flex-1">
      <view class="relative">
        <!-- 上传区域 -->
        <view
          class="w-full h-32 bg-gray-50 border border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center transition-all duration-300 active:scale-95"
          :class="frontImage?.tempFilePath ? 'border-theme-blue bg-theme-blue/5' : 'active:border-theme-blue active:bg-theme-blue/5'"
          @click="handleUploadFront"
        >
          <!-- 已上传状态 -->
          <view v-if="frontImage?.tempFilePath" class="relative w-full h-full">
            <image :src="frontImage.tempFilePath" mode="aspectFill" class="w-full h-full rounded-lg object-cover" />
            <!-- 删除按钮 -->
            <view class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 active:scale-90 z-10" @click.stop="handleDeleteFront">
              <text class="i-mdi-close text-white text-sm"></text>
            </view>
            <!-- 成功标识 -->
            <view class="absolute bottom-1 right-1 w-5 h-5 bg-theme-green rounded-full flex items-center justify-center">
              <text class="i-mdi-check text-white text-xs"></text>
            </view>
          </view>

          <!-- 未上传状态 -->
          <view v-else class="flex flex-col items-center">
            <view class="w-10 h-10 bg-theme-blue/10 rounded-full flex items-center justify-center mb-2">
              <text class="i-mdi-camera-plus text-theme-blue text-xl"></text>
            </view>
            <text class="text-xs text-gray-600 font-medium">上传正面</text>
            <text class="text-xs text-gray-400 mt-1">人像面</text>
          </view>
        </view>

        <!-- 标题 -->
        <view class="text-center mt-2">
          <text class="text-xs text-gray-500">身份证正面</text>
        </view>
      </view>
    </view>

    <!-- 身份证背面上传 -->
    <view class="flex-1">
      <view class="relative">
        <!-- 上传区域 -->
        <view
          class="w-full h-32 bg-gray-50 border border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center transition-all duration-300 active:scale-95"
          :class="backImage?.tempFilePath ? 'border-theme-blue bg-theme-blue/5' : 'active:border-theme-blue active:bg-theme-blue/5'"
          @click="handleUploadBack"
        >
          <!-- 已上传状态 -->
          <view v-if="backImage?.tempFilePath" class="relative w-full h-full">
            <image :src="backImage.tempFilePath" mode="aspectFill" class="w-full h-full rounded-lg object-cover" />
            <!-- 删除按钮 -->
            <view class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 active:scale-90 z-10" @click.stop="handleDeleteBack">
              <text class="i-mdi-close text-white text-sm"></text>
            </view>
            <!-- 成功标识 -->
            <view class="absolute bottom-1 right-1 w-5 h-5 bg-theme-green rounded-full flex items-center justify-center">
              <text class="i-mdi-check text-white text-xs"></text>
            </view>
          </view>

          <!-- 未上传状态 -->
          <view v-else class="flex flex-col items-center">
            <view class="w-10 h-10 bg-theme-blue/10 rounded-full flex items-center justify-center mb-2">
              <text class="i-mdi-camera-plus text-theme-blue text-xl"></text>
            </view>
            <text class="text-xs text-gray-600 font-medium">上传背面</text>
            <text class="text-xs text-gray-400 mt-1">国徽面</text>
          </view>
        </view>

        <!-- 标题 -->
        <view class="text-center mt-2">
          <text class="text-xs text-gray-500">身份证背面</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { UserTypes } from '@/monkey/types';

  interface Props {
    frontImage?: UserTypes.UserAuthFormData['idCardFront'];
    backImage?: UserTypes.UserAuthFormData['idCardBack'];
  }

  interface Emits {
    (e: 'update:frontImage', value: Props['frontImage']): void;
    (e: 'update:backImage', value: Props['backImage']): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    frontImage: () => ({ tempFilePath: '' }),
    backImage: () => ({ tempFilePath: '' }),
  });

  // 初始化身份证信息
  const frontImageInfo = ref<UserTypes.UserAuthFormData['idCardFront']>({ tempFilePath: '', name: '', card: '', address: '', birthday: '', sex: '', fork: '' });
  const backImageInfo = ref<UserTypes.UserAuthFormData['idCardBack']>({ tempFilePath: '' });

  const emit = defineEmits<Emits>();

  // 上传正面
  const handleUploadFront = () => {
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera', 'album'],
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0];
        console.log('🚀 ~ success: ~ tempFilePath:', tempFilePath);
        monkey.$helper.toast.loading('识别中...');
        try {
          const ocrResponse = await monkey.$api.auth.getOcrCardRecognition(tempFilePath);
          console.log('🚀 ~ success: ~ ocrResponse:', ocrResponse);
          if (ocrResponse.errcode == 200) {
            // 合并现有数据和OCR识别结果
            const updatedFrontData: UserTypes.UserAuthFormData['idCardFront'] = Object.assign({}, frontImageInfo.value, { ...ocrResponse.data, tempFilePath });
            // 上传图片信息到服务器
            monkey.$helper.toast.loading('上传中...');
            try {
              // 上传图片信息到服务器
              const uploadResponse = await monkey.$api.auth.uploadImage(tempFilePath);
              // 如果上传成功，则更新数据
              if (uploadResponse.errcode == 200 && uploadResponse.data) {
                emit('update:frontImage', updatedFrontData);
                emit('uploadFrontSuccess', uploadResponse.data);
                monkey.$helper.toast.success('识别成功');
              }
            } catch (error) {
              console.error('图片上传失败:', error);
              monkey.$helper.toast.hideLoading();
              monkey.$helper.toast.error('图片上传失败，请重试');
            }
          } else if (ocrResponse.errcode == 8000) {
            monkey.$helper.toast.error(ocrResponse.msg);
            // 弹出登录弹窗
            monkey.$stores.useAuthModalStore().open();
          } else {
            monkey.$helper.toast.hideLoading();
            monkey.$helper.toast.error(ocrResponse.msg || '识别失败');
          }
        } catch (err) {
          console.error('图片识别失败:', err);
          monkey.$helper.toast.hideLoading();
          monkey.$helper.toast.error('图片识别失败，请重试');
        }
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        monkey.$helper.toast.error('请选择图片');
      },
    });
  };

  // 上传背面
  const handleUploadBack = () => {
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera', 'album'],
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0];
        // 合并现有数据和新的图片路径
        const updatedBackData: UserTypes.UserAuthFormData['idCardBack'] = Object.assign({}, backImageInfo.value, { tempFilePath });
        monkey.$helper.toast.loading('上传中...');
        try {
          // 上传图片信息到服务器
          const uploadResponse = await monkey.$api.auth.uploadImage(tempFilePath);
          // 如果上传成功，则更新数据
          if (uploadResponse.errcode === 200 && uploadResponse.data) {
            emit('update:backImage', updatedBackData);
            emit('uploadBackSuccess', uploadResponse.data);
          } else if (uploadResponse.errcode == 8000) {
            monkey.$helper.toast.error(uploadResponse.msg);
            // 弹出登录弹窗
            monkey.$stores.useAuthModalStore().open();
          }
        } catch (error) {
          console.error('图片识别失败:', error);
          monkey.$helper.toast.hideLoading();
          monkey.$helper.toast.error('图片识别失败，请重试');
        }
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        monkey.$helper.toast.error('请选择图片');
      },
    });
  };

  // 删除正面
  const handleDeleteFront = async (event: any) => {
    const modal = await monkey.$helper.toast.modal({
      title: '确认删除',
      content: '确定要删除身份证正面照片吗？',
      confirmText: '删除',
      confirmColor: '#ff4757',
      cancelText: '取消',
    });
    if (modal) emit('update:frontImage', Object.assign({}, frontImageInfo.value));
  };

  // 删除背面
  const handleDeleteBack = async (event: any) => {
    const modal = await monkey.$helper.toast.modal({
      title: '确认删除',
      content: '确定要删除身份证背面照片吗？',
      confirmText: '删除',
      confirmColor: '#ff4757',
      cancelText: '取消',
    });
    if (modal) emit('update:backImage', Object.assign({}, backImageInfo.value));
  };
</script>

<style lang="scss" scoped>
  // 可以在这里添加额外的样式
</style>
