<template>
  <ly-layout>
    <!-- 验证成功状态 -->
    <view class="flex flex-col items-center justify-center px-6 mt-36" v-if="detail.code == 0">
      <!-- 成功图标 -->
      <view class="mb-8">
        <image :src="monkey.$url.cdn('RElSRV85YjZiZTU5MGM4YTA0ZWE0YThjMWI5ZjUxM2NkYmViNOaguOi6q-aIkOWKny5wbmc=')" class="w-32 h-32 mx-auto"></image>
      </view>

      <!-- 成功标题 -->
      <view class="text-2xl font-semibold text-theme-blue mb-4 text-center"> 验证成功 </view>

      <!-- 成功消息 -->
      <view class="text-gray-600 text-center mb-12 max-w-sm leading-relaxed" v-if="detail.subMsg">
        {{ detail.subMsg }}
      </view>

      <!-- 返回按钮 -->
      <view @click="handleBackHome" class="bg-theme-blue hover:bg-theme-blue-600 text-white font-medium py-4 px-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer"> 返回首页 </view>
    </view>

    <!-- 验证失败状态 -->
    <view class="flex flex-col items-center justify-center px-6 mt-36" v-else>
      <!-- 失败图标 -->
      <view class="mb-8">
        <image :src="monkey.$url.cdn('RElSRV9jNzZiMTc3YmM4NGY0ZWRhYmZlYmEwMjA1NzZlMjU2ZeaguOi6q-Wksei0pS5wbmc=')" class="w-32 h-32 mx-auto"></image>
      </view>

      <!-- 失败标题 -->
      <view class="text-2xl font-semibold text-red-600 mb-4 text-center"> 验证失败 </view>

      <!-- 失败消息 -->
      <view class="text-gray-600 text-center mb-6 max-w-sm leading-relaxed"> 请返回上一页点击提交重新认证 </view>

      <!-- 详细消息 -->
      <view class="text-gray-500 text-sm text-center mb-12 max-w-sm leading-relaxed" v-if="detail.subMsg">
        {{ detail.subMsg }}
      </view>

      <!-- 返回按钮 -->
      <view @click="handleBackPage" class="bg-red-500 active:bg-red-600 text-white font-medium py-4 px-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer"> 返回上一页 </view>
    </view>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { UserTypes } from '@/monkey/types';

  // 响应式数据
  const detail = reactive<UserTypes.OcrCardRecognitionResult>({
    subMsg: '',
    sysReqSn: '',
    code: '',
    jyTradeNo: '',
    msg: '',
    timestamp: '',
    sign: '',
    subCode: '',
  });

  // 页面加载时的处理
  onLoad((option) => {
    if (option) {
      Object.assign(detail, option);
      console.log(detail.code);
    }
  });

  // 返回首页
  const handleBackHome = () => {
    monkey.$router.switchTab('/pages/index/index');
  };

  // 返回上一页
  const handleBackPage = () => {
    monkey.$router.navigateBack();
  };
</script>

<style scoped></style>
