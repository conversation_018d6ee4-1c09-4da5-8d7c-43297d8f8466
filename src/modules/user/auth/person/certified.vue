<template>
  <ly-layout>
    <view class="p-4">
      <!-- 认证状态卡片 -->
      <view class="relative overflow-hidden bg-white rounded-2xl shadow-lg mb-6 border border-gray-100">
        <!-- 装饰性背景渐变 -->
        <view class="absolute inset-0 bg-gradient-to-br from-theme-blue/5 via-transparent to-theme-blue/3"></view>

        <!-- 装饰性几何图形 -->
        <view class="absolute top-0 right-0 w-24 h-24 bg-theme-blue/10 rounded-full transform translate-x-12 -translate-y-12"></view>
        <view class="absolute bottom-0 left-0 w-16 h-16 bg-theme-blue/5 rounded-full transform -translate-x-8 translate-y-8"></view>

        <!-- 主要内容区域 -->
        <view class="relative z-10 p-6">
          <!-- 状态图标和标题区域 -->
          <view class="flex flex-col items-center text-center mb-6">
            <!-- 认证状态图标容器 -->
            <view class="relative mb-4">
              <!-- 图标背景光环效果 -->
              <view v-if="userInfo.sfrz == 1" class="absolute inset-0 bg-green-100 rounded-full animate-pulse scale-110"></view>
              <view v-else-if="userInfo.sfrz == 2" class="absolute inset-0 bg-yellow-100 rounded-full animate-pulse scale-110"></view>
              <view v-else class="absolute inset-0 bg-red-100 rounded-full animate-pulse scale-110"></view>

              <!-- 认证状态图标 -->
              <view class="relative w-[120px] h-[120px] flex items-center justify-center rounded-full border-4 border-white shadow-lg">
                <view v-if="userInfo.sfrz == 1" class="w-full h-full rounded-full flex items-center justify-center">
                  <!-- <text class="i-mdi-shield-check text-white text-4xl"></text> -->
                  <image class="size-full" mode="widthFix" :src="monkey.$url.cdn('RElSRV9iY2Q4M2UyNGRjMmM0M2E4YTY5NDM1NWRhNTkzODUwNeW3suiupOivgSAoMikucG5n')" />
                </view>
                <view v-else-if="userInfo.sfrz == 2" class="w-full h-full rounded-full flex items-center justify-center">
                  <!-- <text class="i-mdi-clock-outline text-white text-4xl"></text> -->
                  <image class="size-full" mode="widthFix" :src="monkey.$url.cdn('RElSRV84ZmMzOGJlYzE0OGM0NDA4OTNhNmE0YWQ3OGE5N2E2NuiupOivgeS4rSAoMikucG5n')" />
                </view>
                <view v-else class="w-full h-full bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center">
                  <text class="i-mdi-alert-circle-outline text-white text-4xl"></text>
                </view>
              </view>
            </view>

            <!-- 认证状态标题 -->
            <view class="mb-3">
              <text v-if="userInfo.sfrz == 1" class="text-lg font-bold text-green-600">认证成功</text>
              <text v-else-if="userInfo.sfrz == 2" class="text-lg font-bold text-yellow-600">审核中</text>
              <text v-else class="text-lg font-bold text-red-600">未认证</text>
            </view>

            <!-- 认证状态描述 -->
            <view class="mb-4">
              <text v-if="userInfo.sfrz == 1" class="text-gray-600 text-sm leading-relaxed">您的身份信息已通过系统验证，可以正常使用所有功能</text>
              <text v-else-if="userInfo.sfrz == 2" class="text-gray-600 text-sm leading-relaxed">您的身份信息正在审核中，请耐心等待审核结果</text>
              <text v-else class="text-gray-600 text-sm leading-relaxed">您的身份信息未认证，请提交认证材料</text>
            </view>

            <!-- 认证时间信息 -->
            <view class="flex items-center justify-center bg-gray-50 rounded-full px-4 py-2">
              <text class="i-mdi-calendar-clock text-theme-blue text-sm mr-2"></text>
              <text class="text-xs text-gray-500">认证时间：{{ monkey.$dayjs(userInfo.modifiedTime).format('YYYY-MM-DD HH:mm:ss') }}</text>
            </view>
          </view>

          <!-- 状态指示条 -->
          <view class="flex items-center justify-center space-x-2 mt-4">
            <view :class="userInfo.sfrz >= 1 ? 'bg-theme-blue' : 'bg-gray-300'" class="w-8 h-1 rounded-full transition-all duration-300"></view>
            <view :class="userInfo.sfrz >= 2 ? 'bg-theme-blue' : 'bg-gray-300'" class="w-8 h-1 rounded-full transition-all duration-300"></view>
            <view :class="userInfo.sfrz == 1 ? 'bg-green-500' : userInfo.sfrz == 2 ? 'bg-yellow-500' : 'bg-red-500'" class="w-8 h-1 rounded-full transition-all duration-300"></view>
          </view>
        </view>
      </view>

      <!-- 个人信息卡片 -->
      <view class="bg-white rounded-lg shadow-md overflow-hidden mb-4">
        <view class="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200 p-4">
          <view class="flex items-center text-gray-700">
            <view class="flex items-center">
              <div class="w-1 h-3 bg-theme-blue rounded-full mr-2"></div>
              <text class="text-sm font-semibold">个人信息</text>
            </view>
          </view>
        </view>

        <!-- 预览内容 -->
        <view class="p-4 space-y-4" v-if="!isPersonalInfoExpanded">
          <!-- 姓名预览 -->
          <view class="flex items-center justify-between py-3">
            <view class="flex items-center">
              <text class="i-mdi-account text-theme-blue text-xl mr-2"></text>
              <text class="text-gray-600 font-medium text-sm">真实姓名</text>
            </view>
            <text class="text-gray-800 font-semibold text-sm">{{ userInfo.zsxm }}</text>
          </view>

          <!-- 展开按钮 -->
          <view @click="togglePersonalInfo" class="flex items-center justify-center py-2 bg-gray-50 rounded-md mt-2">
            <text class="text-xs text-gray-500 mr-1">查看更多信息</text>
            <text class="i-mdi-chevron-down text-gray-500 text-sm"></text>
          </view>
        </view>

        <!-- 完整内容 -->
        <view class="overflow-hidden transition-all duration-300" :class="[isPersonalInfoExpanded ? 'max-h-auto' : 'max-h-0']">
          <view class="p-4 space-y-4">
            <!-- 姓名 -->
            <view class="flex items-center justify-between py-3 border-b border-gray-100">
              <view class="flex items-center">
                <text class="i-mdi-account text-theme-blue text-xl mr-2"></text>
                <text class="text-gray-600 font-medium text-sm">真实姓名</text>
              </view>
              <text class="text-gray-800 font-semibold text-sm">{{ userInfo.zsxm }}</text>
            </view>

            <!-- 身份证号 -->
            <view class="flex items-center justify-between py-3 border-b border-gray-100">
              <view class="flex items-center">
                <text class="i-mdi-card-account-details text-theme-blue text-xl mr-2"></text>
                <text class="text-gray-600 font-medium text-sm">身份证号</text>
              </view>
              <text class="text-gray-800 font-mono text-sm">{{ monkey.$helper.utils.hideIdCard(userInfo.sfzhm) }}</text>
            </view>

            <!-- 联系电话 -->
            <view class="flex items-center justify-between py-3 border-b border-gray-10">
              <view class="flex items-center">
                <text class="i-mdi-phone text-theme-blue text-xl mr-2"></text>
                <text class="text-gray-600 font-medium text-sm">联系电话</text>
              </view>
              <text class="text-gray-800 font-mono text-sm">{{ monkey.$helper.utils.hidePhone(userInfo.sjh) }}</text>
            </view>

            <!-- 性别 -->
            <view class="flex items-center justify-between py-3 border-b border-gray-100">
              <view class="flex items-center">
                <text class="i-mdi-human-male-female text-theme-blue text-xl mr-2"></text>
                <text class="text-gray-600 font-medium text-sm">性别</text>
              </view>
              <view class="flex items-center">
                <text :class="userInfo.xb === '男' ? 'i-mdi-human-male text-blue-500' : 'i-mdi-human-female text-pink-500'" class="text-sm mr-1"></text>
                <text class="text-gray-800 font-semibold text-sm">{{ userInfo.xb }}</text>
              </view>
            </view>

            <!-- 出生日期 -->
            <view class="flex items-center justify-between py-3 border-b border-gray-100">
              <view class="flex items-center">
                <text class="i-mdi-calendar text-theme-blue text-xl mr-2"></text>
                <text class="text-gray-600 font-medium text-sm">出生日期</text>
              </view>
              <text class="text-gray-800 font-semibold text-sm">{{ userInfo.sr }}</text>
            </view>

            <!-- 民族 -->
            <view class="flex items-center justify-between py-3 border-b border-gray-100">
              <view class="flex items-center">
                <text class="i-mdi-earth text-theme-blue text-xl mr-2"></text>
                <text class="text-gray-600 font-medium text-sm">民族</text>
              </view>
              <text class="text-gray-800 font-semibold text-sm">{{ userInfo.mz }}</text>
            </view>

            <!-- 详细地址 -->
            <view class="flex items-start">
              <text class="i-mdi-home-map-marker text-theme-blue text-xl mr-2 mt-1"></text>
              <view class="flex-1">
                <text class="text-gray-600 text-sm mb-2 font-medium block">详细地址</text>
                <text class="text-gray-800 font-medium leading-relaxed text-sm">{{ userInfo.dz }}</text>
              </view>
            </view>

            <!-- 收起按钮 -->
            <view @click="togglePersonalInfo" class="flex items-center justify-center py-2 bg-gray-50 rounded-md mt-2">
              <text class="text-xs text-gray-500 mr-1">收起</text>
              <text class="i-mdi-chevron-up text-gray-500 text-sm"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 身份证照片卡片 -->
      <view class="bg-white rounded-lg shadow-md overflow-hidden mb-4">
        <view class="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200 p-4">
          <view class="flex items-center text-gray-700">
            <view class="flex items-center">
              <div class="w-1 h-3 bg-theme-blue rounded-full mr-2"></div>
              <text class="text-sm font-semibold">身份证照片</text>
            </view>
          </view>
        </view>

        <!-- 预览内容 -->
        <view class="p-4" v-if="!isIdCardPhotosExpanded">
          <!-- 预览图片 -->
          <view class="flex justify-center space-x-2">
            <!-- 身份证正面预览 -->
            <view v-if="!userInfo.sfzzm || userInfo.sfzzm.length == 0" class="bg-gray-100 rounded-lg p-1 w-20 h-12 flex items-center justify-center border-2 border-dashed border-gray-300">
              <text class="text-xs text-gray-500">正面未上传</text>
            </view>
            <div v-else class="bg-gray-100 rounded-lg p-1 w-20 h-12 border-2 border-dashed border-gray-300">
              <image :src="monkey.$url.cdn(userInfo.sfzzm[0].refId)" mode="aspectFit" class="w-full h-full" />
            </div>

            <!-- 身份证背面预览 -->
            <view v-if="!userInfo.sfzbm || userInfo.sfzbm.length == 0" class="bg-gray-100 rounded-lg p-1 w-20 h-12 flex items-center justify-center border-2 border-dashed border-gray-300">
              <text class="text-xs text-gray-500">背面未上传</text>
            </view>
            <div v-else class="bg-gray-100 rounded-lg p-1 w-20 h-12 border-2 border-dashed border-gray-300">
              <image :src="monkey.$url.cdn(userInfo.sfzbm[0].refId)" mode="aspectFit" class="w-full h-full" />
            </div>
          </view>

          <!-- 展开按钮 -->
          <view @click="toggleIdCardPhotos" class="flex items-center justify-center py-2 bg-gray-50 rounded-md mt-3">
            <text class="text-xs text-gray-500 mr-1">查看身份证照片</text>
            <text class="i-mdi-chevron-down text-gray-500 text-sm"></text>
          </view>
        </view>

        <!-- 完整内容 -->
        <view class="overflow-hidden transition-all duration-300" :class="[isIdCardPhotosExpanded ? 'max-h-96' : 'max-h-0']">
          <view class="p-4">
            <view class="grid grid-cols-2 gap-4">
              <!-- 身份证正面 -->
              <view class="text-center">
                <view v-if="!userInfo.sfzzm || userInfo.sfzzm.length == 0" class="bg-gray-100 rounded-lg p-1 mb-3 border-2 border-dashed border-gray-300">
                  <text class="i-mdi-card-account-details text-4xl text-gray-400 block mb-2"></text>
                  <text class="text-xs text-gray-500">身份证正面</text>
                </view>
                <div v-else class="bg-gray-100 rounded-lg p-1 mb-3 border-2 border-dashed border-gray-300">
                  <image @click="monkey.$helper.utils.previewImage(monkey.$url.cdn(userInfo.sfzzm[0].refId))" :src="monkey.$url.cdn(userInfo.sfzzm[0].refId)" mode="widthFix" class="size-full" />
                </div>
                <view class="flex items-center justify-center">
                  <text class="i-mdi-check-circle text-theme-blue text-sm mr-1"></text>
                  <text class="text-sm text-theme-blue font-medium">{{ userInfo.sfzzm.length == 0 ? '未上传' : '已上传' }}</text>
                </view>
              </view>

              <!-- 身份证背面 -->
              <view class="text-center">
                <view v-if="!userInfo.sfzbm || userInfo.sfzbm.length == 0" class="bg-gray-100 rounded-lg p-1 mb-3 border-2 border-dashed border-gray-300">
                  <text class="i-mdi-card-account-details text-4xl text-gray-400 block mb-2"></text>
                  <text class="text-xs text-gray-500">身份证背面</text>
                </view>
                <div v-else class="bg-gray-100 rounded-lg p-1 mb-3 border-2 border-dashed border-gray-300">
                  <image @click="monkey.$helper.utils.previewImage(monkey.$url.cdn(userInfo.sfzbm[0].refId))" :src="monkey.$url.cdn(userInfo.sfzbm[0].refId)" mode="widthFix" class="size-full" />
                </div>
                <view class="flex items-center justify-center">
                  <text class="i-mdi-check-circle text-theme-blue text-sm mr-1"></text>
                  <text class="text-sm text-theme-blue font-medium">{{ userInfo.sfzbm.length == 0 ? '未上传' : '已上传' }}</text>
                </view>
              </view>
            </view>

            <!-- 收起按钮 -->
            <view @click="toggleIdCardPhotos" class="flex items-center justify-center py-2 bg-gray-50 rounded-md mt-4">
              <text class="text-xs text-gray-500 mr-1">收起</text>
              <text class="i-mdi-chevron-up text-gray-500 text-sm"></text>
            </view>
          </view>
        </view>
      </view>
      <ly-line-bar :height="100" />
    </view>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { UserTypes } from '@/monkey/types';

  // 模拟已认证的用户信息数据
  const { user: userInfo } = storeToRefs(monkey.$stores.useUserStore());

  // 控制个人信息卡片的展开/折叠状态
  const isPersonalInfoExpanded = ref(false);

  // 切换个人信息卡片的展开/折叠状态
  const togglePersonalInfo = () => {
    isPersonalInfoExpanded.value = !isPersonalInfoExpanded.value;
  };

  // 控制身份证照片卡片的展开/折叠状态
  const isIdCardPhotosExpanded = ref(false);

  // 切换身份证照片卡片的展开/折叠状态
  const toggleIdCardPhotos = () => {
    isIdCardPhotosExpanded.value = !isIdCardPhotosExpanded.value;
  };

  // 页面加载时获取用户认证信息
  onLoad(() => {
    monkey.$stores.useUserStore().getCurrentUserInfo();
  });
</script>

<style scoped lang="scss"></style>
