<template>
  <ly-layout>
    <view class="p-4">
      <view class="mb-4 p-4 py-2 bg-white rounded-lg shadow-md">
        <uni-forms ref="formRef" :modelValue="formData" :label-position="'top'" :label-width="200" :border="true">
          <!-- 用户类型 
          <uni-forms-item label="用户类型" name="userType" required>
            <uni-data-checkbox v-model="formData.userType" :localdata="userTypeOptions" placeholder="请选择用户类型" mode="button" :border="false" />
            <div class="h-[1px] bg-gray-200 mt-2"></div>
          </uni-forms-item>-->

          <!-- 上传身份证正反面 -->
          <uni-forms-item label="上传身份证正反面" name="idCardFront" required>
            <user-idcard v-model:front-image="formData.idCardFront" v-model:back-image="formData.idCardBack" @uploadFrontSuccess="uploadFrontSuccess" @uploadBackSuccess="uploadBackSuccess" />
          </uni-forms-item>
          <!-- 真实姓名 -->
          <uni-forms-item label="真实姓名" name="idCardFront.name" required>
            <uni-easyinput disabled v-model="formData.idCardFront.name" placeholder="请输入真实姓名" trim="both" :inputBorder="false" />
          </uni-forms-item>

          <!-- 身份证号 -->
          <uni-forms-item label="身份证号" name="idCardFront.card" required>
            <uni-easyinput disabled v-model="formData.idCardFront.card" placeholder="请输入身份证号" trim="both" :inputBorder="false" />
          </uni-forms-item>

          <!-- 详细地址 -->
          <uni-forms-item label="详细地址" name="idCardFront.address" required>
            <uni-easyinput :disabled="visible" v-model="formData.idCardFront.address" placeholder="请输入详细地址" :inputBorder="false" />
          </uni-forms-item>

          <!-- 性别 -->
          <uni-forms-item label="性别" name="idCardFront.sex" required>
            <uni-data-checkbox disabled v-model="formData.idCardFront.sex" :localdata="genderOptions" placeholder="请选择性别" mode="button" :border="false" />
          </uni-forms-item>

          <!-- 出生日期 -->
          <uni-forms-item label="出生日期" name="idCardFront.birthday" required>
            <uni-easyinput disabled v-model="formData.idCardFront.birthday" placeholder="请输入出生日期" trim="both" :inputBorder="false" />
          </uni-forms-item>

          <!-- 民族 -->
          <uni-forms-item label="民族" name="idCardFront.fork" required>
            <uni-easyinput disabled v-model="formData.idCardFront.fork" placeholder="请输入民族" trim="both" :inputBorder="false" />
          </uni-forms-item>
        </uni-forms>
      </view>
      <ly-line-bar :height="150" />
    </view>
    <ly-fixed-btns :buttons="buttons" />
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { UserTypes, UploadResult } from '@/monkey/types';
  import UserIdcard from '../components/user-idcard.vue';

  // 是否显示授权弹窗 解决页面穿透后弹出输入框
  const { visible } = storeToRefs(monkey.$stores.useAuthModalStore());

  const { user: userInfo } = storeToRefs(monkey.$stores.useUserStore());

  // 用户类型选项
  const userTypeOptions = [
    { text: '种植户', value: '1' },
    { text: '非种植户', value: '2' },
  ];

  // 性别选项
  const genderOptions = [
    { text: '男', value: '男' },
    { text: '女', value: '女' },
  ];

  // 表单数据
  const formData = reactive<UserTypes.UserAuthFormData>({
    // 身份证信息
    idCardFront: {
      phone: '',
      tempFilePath: '',
      card: '',
      name: '',
      address: '',
      birthday: '',
      sex: '',
      fork: '',
    },
    idCardBack: {
      tempFilePath: '',
    },
    frontImage: {
      refId: '',
    },
    backImage: {
      refId: '',
    },
  });

  const buttons = [
    {
      text: '确认信息，并进行人脸识别',
      icon: 'i-mdi-account-check',
      type: 'primary',
      click: () => submitForm(),
    },
  ];

  // 表单引用
  const formRef = ref<any>(null);

  // 身份证图片类型
  type imgType = UserTypes.UserAuthFormData['idCardFront'] | UserTypes.UserAuthFormData['idCardBack'];

  /**
   * 处理身份证照片上传
   * @param imagePath 身份证图片路径
   * @param type 照片类型 'front' 或 'back'
   */
  const handleImageUpload = async (item: imgType, type: 'front' | 'back') => {
    // 更新上传图片信息
    const targetKey = type === 'front' ? 'frontImage' : 'backImage';
    // 使用lodash的pick只选取formData中已有的keys
    const validData = monkey.$lodash.merge(formData[targetKey], monkey.$lodash.pick(item, Object.keys(formData[targetKey])));
    // 合并有效数据
    monkey.$helper.toast.success(type === 'front' ? '身份证正面照片上传成功' : '身份证背面照片上传成功');
  };

  /**
   * 处理身份证正面照片上传成功
   * @param image 身份证正面信息
   */
  const uploadFrontSuccess = (image: UserTypes.UserAuthFormData['idCardFront']) => {
    handleImageUpload(image, 'front');
  };

  /**
   * 处理身份证背面照片上传成功
   * @param imagePath 身份证背面信息
   */
  const uploadBackSuccess = (image: UserTypes.UserAuthFormData['idCardBack']) => {
    handleImageUpload(image, 'back');
  };

  /**
   * 提交身份认证表单
   */
  const submitForm = async () => {
    console.log('🚀 ~ submitForm ~ formData:', formData);
    try {
      // 检查身份证是否已上传
      if (!formData.idCardFront?.tempFilePath || !formData.idCardBack?.tempFilePath) {
        return monkey.$helper.toast.error('请上传身份证正反面照片');
      }

      // 检查图片上传状态
      if (!formData.frontImage?.refId || !formData.backImage?.refId) {
        return monkey.$helper.toast.error('身份证照片上传未完成，请稍候再试');
      }

      // 校验表单
      const valid = await formRef.value.validate();

      if (valid) {
        // 显示加载提示
        monkey.$helper.toast.loading('提交认证信息...');

        try {
          const { errcode, data, msg } = await monkey.$api.user.userAuth({
            cardId: formData.idCardFront.card,
            name: formData.idCardFront.name,
            sex: formData.idCardFront.sex,
            fork: formData.idCardFront.fork,
            birthday: formData.idCardFront.birthday,
            address: formData.idCardFront.address,
            sfzzm: formData.frontImage.refId,
            sfzbm: formData.backImage.refId,
          });

          if (errcode == 200) {
            monkey.$helper.toast.success('认证信息提交成功');
            setTimeout(() => {
              monkey.$helper.toast.loading('跳转认证页面...');
              monkey.$router.navigateTo('/modules/user/webview/index?url=' + encodeURIComponent(data.data.url), { delay: 1000 });
            }, 2000);
          }
        } catch (error) {
          console.error('提交认证信息失败:', error);
          monkey.$helper.toast.hideLoading();
          monkey.$helper.toast.error('提交失败，请稍后重试');
        }
      }
    } catch (err) {
      console.error('表单验证错误:', err);
      monkey.$helper.toast.hideLoading();
      monkey.$helper.toast.error('表单信息有误，请检查');
    }
  };
</script>

<style lang="scss" scoped>
  :deep(.is-disabled) {
    background-color: #fff !important;
    color: #333 !important;
  }
</style>
