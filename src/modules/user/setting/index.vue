<template>
  <ly-layout>
    <div class="p-4">
      <div class="text-sm text-gray-500 mb-2 flex items-center">
        <div class="w-1 h-3 bg-theme-blue rounded-full mr-2"></div>
        基础信息
      </div>
      <div class="rounded-lg bg-white px-2 py-2 shadow-sm">
        <van-cell-group :border="false">
          <van-cell icon="manager-o" title="昵称" size="large" :value="userInfo.nc" is-link />
          <van-cell icon="phone-o" title="手机号" size="large" :value="monkey.$helper.utils.hidePhone(userInfo.sjh)" />
          <van-cell v-if="userInfo.zsxm" icon="contact-o" title="真实姓名" size="large" :value="userInfo.zsxm" />
          <van-cell v-if="userInfo.sfzhm" icon="idcard" title="身份证号" size="large" :value="monkey.$helper.utils.hideIdCard(userInfo.sfzhm)" :border="false" />
        </van-cell-group>
      </div>
      <div class="text-sm text-gray-500 mb-2 mt-4 flex items-center">
        <div class="w-1 h-3 bg-theme-blue rounded-full mr-2"></div>
        认证信息
      </div>
      <div class="rounded-lg bg-white px-2 py-2 shadow-sm">
        <van-cell-group :border="false">
          <van-cell icon="diamond-o" title="企业认证" size="large">
            <text class="text-sm text-gray-500">未认证</text>
          </van-cell>
          <van-cell icon="user-o" title="个人认证" size="large" :border="false">
            <text class="text-sm" :class="monkey.$helper.utils.getAuthStatusClass(userInfo.sfrz)">{{ monkey.$helper.utils.getAuthStatusText(userInfo.sfrz) }}</text>
          </van-cell>
        </van-cell-group>
      </div>
      <!-- 退出按钮 -->
      <div class="mt-16">
        <div class="cu-cancel-btn !rounded-full" @click="handleLogout">
          <i class="i-mdi-logout-variant mr-2 text-xl"></i>
          立即退出
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  const userInfo = computed(() => monkey.$stores.useUserStore().user);

  /**
   * 获取认证状态
   * @param status 认证状态
   * @returns 认证状态
   */
  const getAuthStatus = (status: number) => {
    switch (status) {
      case 0:
        return '未认证';
      case 1:
        return '已认证';
      case 2:
        return '审核中';
      default:
        return '未认证';
    }
  };

  // 退出登录
  const handleLogout = async () => {
    const modal = await monkey.$helper.toast.modal({
      title: '提示',
      content: '确定要退出登录吗？',
    });

    if (modal) {
      // 调用退出登录方法
      monkey.$stores.useUserStore().logout();
      monkey.$helper.toast.success('退出成功');
      monkey.$router.switchTab('/pages/index/index', { delay: 1000 });
    }
  };
</script>

<style scoped lang="scss">
  :deep(.van-cell__title) {
    font-size: 14px !important;
  }

  :deep(.van-cell__value) {
    font-size: 14px !important;
  }
</style>
