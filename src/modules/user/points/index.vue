<template>
  <ly-layout>
    <!-- 顶部积分卡片 -->
    <div class="relative overflow-hidden">
      <div class="bg-gradient-to-br from-theme-blue to-theme-blue text-white relative">
        <!-- 装饰性背景图案 -->
        <div class="absolute inset-0 opacity-10">
          <div class="absolute top-0 right-0 w-32 h-32 bg-white rounded-full transform translate-x-16 -translate-y-16"></div>
          <div class="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full transform -translate-x-12 translate-y-12"></div>
        </div>

        <!-- 主要内容 -->
        <div class="relative z-10 p-4">
          <!-- 顶部按钮组 -->
          <div class="flex justify-between items-center mb-4 px-8">
            <div class="flex items-center bg-white/15 active:bg-white/25 backdrop-blur-sm rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-sm">
              <text class="i-mdi-gift-outline mr-2 text-sm"></text>
              积分兑换
            </div>
            <div class="flex items-center bg-white/15 active:bg-white/25 backdrop-blur-sm rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-sm">
              <text class="i-mdi-information-outline mr-2 text-sm"></text>
              积分规则
            </div>
          </div>

          <!-- 当前积分展示 -->
          <div class="text-center mb-4">
            <div class="text-sm opacity-90 mb-3">当前积分</div>
            <div class="text-3xl font-bold mb-2 tracking-wider drop-shadow-sm">{{ totalPoints }}</div>
            <div class="text-xs opacity-80">积分永久有效</div>
          </div>

          <!-- 累计统计卡片 -->
          <div class="flex justify-center space-x-8">
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-4 text-center shadow-sm">
              <div class="text-xl font-bold mb-1">{{ totalEarned }}</div>
              <div class="text-xs opacity-80">累计获得</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-4 text-center shadow-sm">
              <div class="text-xl font-bold mb-1">{{ totalSpent }}</div>
              <div class="text-xs opacity-80">累计消耗</div>
            </div>
          </div>
        </div>
      </div>
    </div>
 
    <!-- 积分明细 -->
    <div class="px-4 py-4 bg-white">
      <ly-title title="积分明细" />
      <!-- 积分记录列表 -->
      <div class="">
        <div v-for="record in pointsRecords" :key="record.id" class="border-b border-gray-100 p-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <!-- 图标 -->
              <div class="w-10 h-10 rounded-lg flex items-center justify-center mr-3" :style="{ backgroundColor: record.iconBg }">
                <text :class="record.icon" class="text-white text-lg"></text>
              </div>

              <!-- 描述信息 -->
              <div>
                <div class="font-medium text-gray-800 text-sm mb-1">
                  {{ record.description }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ record.createdAt }}
                </div>
              </div>
            </div>

            <!-- 积分变动 -->
            <div class="font-bold text-lg" :class="record.type === 'income' ? 'text-emerald-500' : 'text-orange-500'">{{ record.type === 'income' ? '+' : '-' }} {{ record.points }}</div>
          </div>
        </div>
      </div>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';

  // 积分记录接口
  interface PointsRecord {
    id: string;
    type: 'income' | 'expense';
    points: number;
    description: string;
    createdAt: string;
    icon: string;
    iconBg: string;
  }

  // 响应式数据
  const totalPoints = ref('1,280');
  const totalEarned = ref('650');
  const totalSpent = ref('370');

  // 积分记录数据
  const pointsRecords = reactive<PointsRecord[]>([
    {
      id: '1',
      type: 'income',
      points: 58,
      description: '购物返积分',
      createdAt: '2025-07-09 10:30',
      icon: 'i-mdi-gift',
      iconBg: '#10b981',
    },
    {
      id: '2',
      type: 'expense',
      points: 200,
      description: '兑换优惠券',
      createdAt: '2025-07-07 15:00',
      icon: 'i-mdi-gift',
      iconBg: '#f97316',
    },
    {
      id: '3',
      type: 'income',
      points: 10,
      description: '连续签到奖励',
      createdAt: '2025-07-07 09:00',
      icon: 'i-mdi-gift',
      iconBg: '#10b981',
    },
    {
      id: '4',
      type: 'income',
      points: 100,
      description: '邀请好友注册',
      createdAt: '2025-07-06 20:15',
      icon: 'i-mdi-gift',
      iconBg: '#10b981',
    },
    {
      id: '5',
      type: 'expense',
      points: 50,
      description: '积分抽奖',
      createdAt: '2025-07-05 11:45',
      icon: 'i-mdi-gift',
      iconBg: '#f97316',
    },
  ]);
</script>

<style lang="scss" scoped>
</style>
