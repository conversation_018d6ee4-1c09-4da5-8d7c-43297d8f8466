<template>
  <view v-if="times.length > 0" class="grid grid-cols-3 gap-2">
    <view v-for="(item, index) in times" :key="index">
      <view
        @click="handleSelectTime(item, index)"
        :class="['px-3.5 py-2 text-sm transition-all duration-300 box-border border border-gray-200 rounded-lg flex items-center justify-center cursor-pointer relative', getTimeItemClass(item, index)]"
      >
        <view class="flex flex-col items-center">
          <text class="text-xs">{{ item.time }}</text>
        </view>
        <!-- 剩余数量标签 -->
        <view
          v-if="item.count !== undefined"
          class="absolute top-0 right-0 rounded-tl-lg bg-gradient-to-r from-red-500 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full min-w-4 h-4 flex items-center justify-center shadow-lg transform transition-all duration-300 hover:scale-110"
        >
          <text class="leading-none">{{ item.count }}</text>
        </view>
      </view>
    </view>
  </view>
  <view v-if="!isLoading && times.length === 0" class="flex flex-col items-center justify-center gap-2">
    <text class="i-mdi-clock-outline text-xl text-gray-400"></text>
    <text class="text-gray-400 text-sm">暂无预约时间段</text>
  </view>
  <ly-loading v-if="isLoading" />
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { HomeTypes } from '@/monkey/types';

  const props = withDefaults(
    defineProps<{
      times: HomeTypes.InboundTimeItem[];
      type: 'am' | 'pm';
      selectedIndex?: number | null;
      isLoading?: boolean;
    }>(),
    {
      times: () => [],
      type: 'am',
      selectedIndex: null,
      isLoading: false,
    },
  );

  const emit = defineEmits<{
    (e: 'selectTime', item: HomeTypes.InboundTimeItem, index: number): void;
  }>();

  /**
   * 处理时间选择
   */
  const handleSelectTime = (item: HomeTypes.InboundTimeItem, index: number) => {
    // 如果时间段已禁用或过期，不允许选择
    if (item.isDisabled || item.isExpired || item.count === 0) {
      const message = item.isExpired ? '该时间段已过期' : item.isDisabled ? '该时间段已禁用' : '该时间段已满';
      monkey.$helper.toast.error(message);
      return;
    }

    emit('selectTime', item, index);
  };

  /**
   * 获取时间项的样式类
   */
  const getTimeItemClass = (item: HomeTypes.InboundTimeItem, index: number) => {
    // 如果已禁用或过期或已满
    if (item.isDisabled || item.isExpired || item.count === 0) {
      return 'bg-gray-100 text-gray-400 cursor-not-allowed opacity-50';
    }

    // 如果是选中状态
    if (props.selectedIndex === index) {
      return 'bg-theme-blue text-white shadow-md';
    }

    // 默认状态
    return 'bg-gray-50 text-gray-700 hover:bg-gray-100 active:bg-gray-200';
  };
</script>
