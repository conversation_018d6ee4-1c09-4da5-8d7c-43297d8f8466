<template>
  <view v-if="currentDates.length > 0" class="grid items-center" :class="`grid-cols-${cols} gap-${gap}`">
    <view
      v-for="(date, index) in currentDates"
      :key="date.date"
      @click="selectDate(date, index)"
      :class="[
        'flex-shrink-0 rounded-lg p-3 transition-all duration-300 relative',
        date.isExpired || date.isDisabled
          ? 'bg-gray-100 text-gray-400 border border-gray-200 cursor-not-allowed opacity-50'
          : selectedDateIndex === index
            ? 'bg-theme-blue-600 text-white shadow-md transform scale-102'
            : 'bg-gray-50 text-gray-700 border border-gray-200 active:bg-gray-100',
      ]"
    >
      <view class="flex flex-col items-center">
        <text :class="['text-xs mb-1', date.isExpired || date.isDisabled ? 'text-gray-400' : selectedDateIndex === index ? 'text-blue-100' : 'text-gray-500']">
          {{ date.week }}
        </text>
        <text :class="['text-xs font-medium', date.isExpired || date.isDisabled ? 'text-gray-400' : selectedDateIndex === index ? 'text-white' : 'text-gray-800']">
          {{ date.date }}
        </text>
      </view>
      <!-- 过期/禁用标识 -->
      <view v-if="date.isExpired || date.isDisabled" class="absolute top-1 right-1">
        <text :class="['text-xs text-gray-400', date.isExpired ? 'i-mdi-close' : 'i-mdi-lock']"></text>
      </view>
    </view>

    <!-- More button -->
    <view v-if="isMore" class="flex-shrink-0 rounded-lg p-3 w-16 bg-blue-50 text-blue-700 border border-blue-200 active:bg-blue-100" @click="onMoreClick">
      <view class="flex flex-col items-center">
        <text class="text-xs mb-1 text-blue-500">更多</text>
        <text class="text-xs font-medium text-blue-800">...</text>
      </view>
    </view>
  </view>
  <view v-if="!isLoading && currentDates.length === 0" class="flex flex-col items-center justify-center gap-2">
    <text class="i-mdi-calendar-blank text-xl text-gray-400"></text>
    <text class="text-gray-400 text-sm">暂无预约日期</text>
  </view>
  <ly-loading v-if="isLoading" />
</template>
<script setup lang="ts">
  import monkey from '@/monkey';
  import { HomeTypes } from '@/monkey/types';

  const props = withDefaults(
    defineProps<{
      selectedDateIndex: number;
      isMore: boolean;
      cols: number;
      gap: number;
      type: 'ck' | 'rk';
      typeName: '出库' | '入库';
    }>(),
    {
      selectedDateIndex: 0,
      isMore: true,
      cols: 0,
      gap: 1.5,
      type: 'rk',
      typeName: '入库',
    },
  );

  const isLoading = ref(false);

  /**
   * 当前日期列表
   */
  const currentDates = ref<HomeTypes.InboundDateItem[]>([]);

  /**
   * 选中日期
   */
  const selectedDate = ref<HomeTypes.InboundDateItem>();

  /**
   * 获取预约日期列表
   */
  const getReservationDateList = async () => {
    try {
      isLoading.value = true;
      const today = monkey.$dayjs().format('YYYY-MM-DD');
      const params = monkey.$helper.param.getAuthineListParams({
        schemaCode: 'crkyysj',
        queryCondition: [
          {
            ...monkey.$helper.param.getAuthineListQueryCondition('Eq', 'sfqy', 12, '[{"key":"qy","value":"启用"}]'),
          },
          {
            ...monkey.$helper.param.getAuthineListQueryCondition('Eq', 'sjlx', 14, `[{"key":"${props.type}","value":"${props.typeName}"}]`),
          },
          {
            ...monkey.$helper.param.getAuthineListQueryCondition('Gt', 'yyrq', 3, today),
          },
        ],
        size: 32,
        page: 0,
      });
      const dateListRes = await monkey.$api.authine.getAuthineList(params);
      if (dateListRes.errcode === 0 && dateListRes.data.content) {
        const today = monkey.$dayjs();
        currentDates.value = dateListRes.data.content
          .map((item: { data: HomeTypes.InboundDateItem }) => {
            const dateObj = monkey.$dayjs(item.data.yyrq);
            return {
              id: item.data.id,
              date: dateObj.format('MM-DD'),
              dateStr: dateObj.format('YYYY-MM-DD'),
              week: dateObj.format('ddd'),
              isToday: dateObj.isSame(today, 'day'),
              isExpired: dateObj.isBefore(today, 'day'),
              isDisabled: item.data.sfqy_key !== 'qy',
            };
          })
          .filter((item) => !item.isExpired);
        if (currentDates.value.length > 0) {
          // 如果存在更多 就截取前cols个
          if (props.isMore) {
            currentDates.value = currentDates.value.slice(0, props.cols - 1);
          }
          selectDate(currentDates.value[0], 0);
        }
      }
    } catch (error) {
      console.error('getReservationDateList error:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const emit = defineEmits<{
    (e: 'selectDate', date: HomeTypes.InboundDateItem, index: number): void;
    (e: 'more'): void;
  }>();

  /**
   * 选中日期
   * @param date 日期
   * @param index 索引
   */
  const selectDate = (date: HomeTypes.InboundDateItem, index: number) => {
    // 如果日期已过期或被禁用，则不允许选择
    if (date.isExpired || date.isDisabled) {
      monkey.$helper.toast.error(`${date.isExpired ? '日期已过期' : '日期已禁用'}`);
      return;
    }
    selectedDate.value = date;
    emit('selectDate', date, index);
  };

  const onMoreClick = () => {
    emit('more');
  };

  // 监听 type 和 typeName 的变化，重新获取数据
  // watch(
  //   () => [props.type, props.typeName],
  //   () => {
  //     getReservationDateList();
  //   },
  //   { immediate: true }
  // );

  onLoad(() => {
    getReservationDateList();
  });
</script>
