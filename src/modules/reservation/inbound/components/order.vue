<template>
  <div class="flex flex-col size-full">
    <scroll-view scroll-y class="size-full">
      <!-- 订单确认卡片 -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <!-- 订单头部 -->
        <div class="bg-gradient-to-r from-theme-blue to-theme-blue-600 px-4 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <i class="i-mdi-package-variant text-white text-xl"></i>
              </div>
              <div>
                <div class="text-white text-sm font-semibold">入库预约订单</div>
                <text class="text-white/80 text-xs">请确认以下预约信息</text>
              </div>
            </div>
          </div>
        </div>

        <!-- 预约时间信息 -->
        <div class="p-4 border-b border-gray-100">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-theme-green/10 rounded-full flex items-center justify-center mr-3">
              <i class="i-mdi-calendar-clock text-theme-green text-lg"></i>
            </div>
            <div class="text-gray-800 font-semibold text-sm">预约时间</div>
          </div>

          <div class="bg-gray-50 rounded-lg p-4 space-y-3">
            <div class="flex items-center justify-between">
              <text class="text-gray-600 text-sm">预约日期</text>
              <text class="text-gray-900 font-medium">{{ formatDate(order.yyrq) }}</text>
            </div>
            <div class="flex items-center justify-between">
              <text class="text-gray-600 text-sm">时间段</text>
              <text class="text-theme-blue font-medium bg-theme-blue/10 px-3 py-1 rounded-full text-sm">
                {{ order.yysjd }}
              </text>
            </div>
          </div>
        </div>

        <!-- 选中产品信息 -->
        <div class="p-4 border-b border-gray-100">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
              <i class="i-mdi-package-variant-closed text-orange-600 text-lg"></i>
            </div>
            <h3 class="text-gray-800 font-semibold text-sm">入库产品</h3>
            <text class="ml-2 bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full"> 共 {{ order.yymx?.length || 0 }} 种 </text>
          </div>

          <div class="space-y-3">
            <!-- 空状态提示 -->
            <div v-if="!order.yymx || order.yymx.length === 0" class="text-center py-8">
              <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <i class="i-mdi-package-variant-closed text-gray-400 text-2xl"></i>
              </div>
              <p class="text-gray-500 text-sm">暂无选中的产品</p>
            </div>

            <!-- 产品列表 -->
            <div v-for="(item, index) in order.yymx" :key="index" class="bg-gray-50 rounded-lg p-4 border border-gray-100 hover:border-theme-blue/30 transition-colors">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <div class="text-gray-900 font-medium text-sm">{{ item.bzmc }}</div>
                    <text class="text-gray-500 text-xs bg-gray-200 px-2 py-0.5 rounded">
                      {{ item.ywsc }}
                    </text>
                  </div>

                  <div class="grid grid-cols-2 gap-3 text-sm">
                    <div class="flex items-center gap-1">
                      <i class="i-mdi-counter text-gray-400"></i>
                      <text class="text-gray-600">数量:</text>
                      <text class="text-gray-900 font-medium">{{ item.rksl || 0 }} 件</text>
                    </div>
                    <div class="flex items-center gap-1">
                      <i class="i-mdi-weight text-gray-400"></i>
                      <text class="text-gray-600">总重量:</text>
                      <text class="text-gray-900 font-medium">{{ item.rkzl || 0 }} kg</text>
                    </div>
                    <div class="flex items-center gap-1">
                      <i class="i-mdi-format-list-bulleted text-gray-400"></i>
                      <text class="text-gray-600">规格:</text>
                      <text class="text-gray-900 font-medium">{{ item.cpgg || '未填写' }}</text>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 身份信息 -->
        <div class="p-6">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
              <i class="i-mdi-account-circle text-purple-600 text-lg"></i>
            </div>
            <h3 class="text-gray-800 font-semibold text-sm">联系信息</h3>
          </div>

          <div class="bg-gray-50 rounded-lg p-4 space-y-4">
            <div class="flex items-center space-x-3">
              <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                <i class="i-mdi-account text-gray-600 text-sm"></i>
              </div>
              <div class="flex-1">
                <div class="text-gray-600 text-xs mb-1">联系人</div>
                <div class="text-gray-900 font-medium">{{ order.yyr || '未填写' }}</div>
              </div>
            </div>

            <div class="flex items-center space-x-3">
              <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                <i class="i-mdi-phone text-gray-600 text-sm"></i>
              </div>
              <div class="flex-1">
                <div class="text-gray-600 text-xs mb-1">联系电话</div>
                <div class="text-gray-900 font-medium font-mono">{{ order.yyrdh || '未填写' }}</div>
              </div>
            </div>

            <div class="flex items-center space-x-3">
              <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                <i class="i-mdi-card-account-details text-gray-600 text-sm"></i>
              </div>
              <div class="flex-1">
                <div class="text-gray-600 text-xs mb-1">身份证号</div>
                <div class="text-gray-900 font-medium font-mono">{{ formatIdCard(order.yyrsfz) || '未填写' }}</div>
              </div>
            </div>

            <div v-if="order.yuany" class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center mt-0.5">
                <i class="i-mdi-note-text text-gray-600 text-sm"></i>
              </div>
              <div class="flex-1">
                <div class="text-gray-600 text-xs mb-1">备注信息</div>
                <div class="text-gray-900 text-sm leading-relaxed">{{ order.yuany }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </scroll-view>
  </div>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { UserTypes } from '@/monkey/types';

  const props = defineProps<{
    order: UserTypes.UserReservationInboundForm;
  }>();

  // 生成订单号
  const orderNumber = computed(() => {
    const now = new Date();
    const dateStr = monkey.$dayjs(now).format('YYYYMMDD');
    const timeStr = monkey.$dayjs(now).format('HHmmss');
    return `IN${dateStr}${timeStr}`;
  });

  // 格式化日期
  const formatDate = (date: string | undefined) => {
    if (!date) return '未选择';
    return monkey.$dayjs(date).format('YYYY年MM月DD日 dddd');
  };

  // 格式化日期时间
  const formatDateTime = (datetime: string | undefined) => {
    if (!datetime) return monkey.$dayjs().format('YYYY-MM-DD HH:mm:ss');
    return monkey.$dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
  };

  // 格式化身份证号（隐藏中间部分）
  const formatIdCard = (idCard: string | undefined) => {
    if (!idCard) return '';
    if (idCard.length !== 18) return idCard;
    return `${idCard.substring(0, 6)}********${idCard.substring(14)}`;
  };
</script>
