# 出入库预约时间段表结构设计方案

## 方案一：基础版表结构设计

### 1. 预约配置表 (appointment_config)
```sql
CREATE TABLE appointment_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    warehouse_id BIGINT NOT NULL UNIQUE COMMENT '仓库ID',
    max_advance_days INT DEFAULT 15 COMMENT '最大可预约天数(当前日期+该天数)',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 2. 时间段模板表 (time_slot_template)
```sql
CREATE TABLE time_slot_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
    warehouse_id BIGINT NOT NULL COMMENT '仓库ID',
    period_type TINYINT NOT NULL COMMENT '时段类型(1:上午 2:下午)',
    period_name VARCHAR(50) NOT NULL COMMENT '时段名称(如: 上午, 下午)',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    max_appointments INT DEFAULT 5 COMMENT '该时间段最大预约数',
    sort_order INT DEFAULT 0 COMMENT '排序字段',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 3. 时间段配置表 (time_slot_config)
```sql
CREATE TABLE time_slot_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '时间段配置ID',
    warehouse_id BIGINT NOT NULL COMMENT '仓库ID',
    config_date DATE NOT NULL COMMENT '配置日期',
    time_slot VARCHAR(50) NOT NULL COMMENT '时间段(如: 09:00-11:00)',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    max_appointments INT DEFAULT 5 COMMENT '该时间段最大预约数',
    used_appointments INT DEFAULT 0 COMMENT '已使用预约数',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 4. 预约主表 (warehouse_appointment)
```sql
CREATE TABLE warehouse_appointment (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '预约ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    warehouse_id BIGINT NOT NULL COMMENT '仓库ID',
    appointment_type TINYINT NOT NULL COMMENT '预约类型(1:入库 2:出库)',
    appointment_date DATE NOT NULL COMMENT '预约日期',
    time_slot VARCHAR(50) NOT NULL COMMENT '时间段(如: 09:00-11:00)',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    status TINYINT DEFAULT 1 COMMENT '预约状态(1:待确认 2:已确认 3:已完成 4:已取消)',
    contact_person VARCHAR(50) NOT NULL COMMENT '联系人',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    goods_info TEXT COMMENT '货物信息(JSON格式)',
    remark TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 5. 自动生成逻辑说明

系统每天定时任务根据以下逻辑自动生成时间段配置：

1. 查询 `appointment_config` 表获取各仓库的最大可预约天数
2. 查询 `time_slot_template` 表获取各仓库的时间段模板
3. 为每个仓库生成从明天开始到 `max_advance_days` 天后的每一天的时间段配置
4. 将生成的配置插入到 `time_slot_config` 表中

---

## 方案二：支持模板选择的表结构设计

### 1. 时间段模板主表 (time_template)
```sql
CREATE TABLE time_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_desc VARCHAR(255) COMMENT '模板描述',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认模板(1:是 0:否)',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用(1:启用 0:禁用)',
    create_user_id BIGINT COMMENT '创建人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 2. 时间段模板明细表 (time_template_detail)
```sql
CREATE TABLE time_template_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '明细ID',
    template_id BIGINT NOT NULL COMMENT '模板ID',
    period_type TINYINT NOT NULL COMMENT '时段类型(1:上午 2:下午)',
    period_name VARCHAR(50) NOT NULL COMMENT '时段名称(如: 上午, 下午)',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    max_appointments INT DEFAULT 5 COMMENT '该时间段最大预约数',
    sort_order INT DEFAULT 0 COMMENT '排序字段',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 3. 仓库模板配置表 (warehouse_template_config)
```sql
CREATE TABLE warehouse_template_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    warehouse_id BIGINT NOT NULL COMMENT '仓库ID',
    template_id BIGINT NOT NULL COMMENT '模板ID',
    max_advance_days INT DEFAULT 15 COMMENT '最大可预约天数(当前日期+该天数)',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 4. 时间段配置表 (time_slot_config)
```sql
CREATE TABLE time_slot_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '时间段配置ID',
    warehouse_id BIGINT NOT NULL COMMENT '仓库ID',
    template_id BIGINT NOT NULL COMMENT '模板ID',
    config_date DATE NOT NULL COMMENT '配置日期',
    time_slot VARCHAR(50) NOT NULL COMMENT '时间段(如: 09:00-11:00)',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    max_appointments INT DEFAULT 5 COMMENT '该时间段最大预约数',
    used_appointments INT DEFAULT 0 COMMENT '已使用预约数',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 5. 预约主表 (warehouse_appointment)
```sql
CREATE TABLE warehouse_appointment (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '预约ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    warehouse_id BIGINT NOT NULL COMMENT '仓库ID',
    template_id BIGINT NOT NULL COMMENT '模板ID',
    appointment_type TINYINT NOT NULL COMMENT '预约类型(1:入库 2:出库)',
    appointment_date DATE NOT NULL COMMENT '预约日期',
    time_slot VARCHAR(50) NOT NULL COMMENT '时间段(如: 09:00-11:00)',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    status TINYINT DEFAULT 1 COMMENT '预约状态(1:待确认 2:已确认 3:已完成 4:已取消)',
    contact_person VARCHAR(50) NOT NULL COMMENT '联系人',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    goods_info TEXT COMMENT '货物信息(JSON格式)',
    remark TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 6. 自动生成逻辑说明

系统每天定时任务根据以下逻辑自动生成时间段配置：

1. 查询 `warehouse_template_config` 表获取各仓库使用的模板和最大预约天数
2. 根据模板ID查询 `time_template_detail` 表获取具体的时间段配置
3. 为每个仓库生成从明天开始到 `max_advance_days` 天后的每一天的时间段配置
4. 将生成的配置插入到 `time_slot_config` 表中

### 7. 设计对比

| 特性 | 方案一 | 方案二 |
|------|--------|--------|
| 复杂度 | 简单 | 复杂 |
| 灵活性 | 一般 | 高 |
| 模板支持 | 无 | 支持多模板 |
| 适用场景 | 单一时间段配置 | 多种时间段模板 |
| 维护成本 | 低 | 较高 |
